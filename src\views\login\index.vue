
<template>
  <div class="flex flex-wrap flex-col w-14 h-48 login">
    <img class="close-btn" :src="bindIcon()" alt="关闭" @click="closeDialog" />
    <div class="flex justify-center items-center flex-1">
      <div class="flex">
        <span class="w-20">账号：</span
        ><el-input
          v-model="username"
          size="mini"
          placeholder="请输入账号"
          maxlength="25"
        ></el-input>
      </div>
    </div>
    <div class="flex justify-center items-center flex-1">
      <div class="flex">
        <span class="w-20">密码：</span
        ><el-input
          v-model="psd"
          type="password"
          size="mini"
          placeholder="请输入密码"
          maxlength="20"
        ></el-input>
      </div>
    </div>
    <div class="flex justify-center items-center flex-1">
      <div>
        <el-button size="mini" @click="reset">重置</el-button>
        <el-button size="mini" @click="submit" type="primary">登录</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, defineEmits } from 'vue'
import { close } from '../../assets/plugin/index.ts'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
let username = ref('')
const emit = defineEmits(['closeLogin'])
let psd = ref('')
const props = defineProps({
  loginFlag: {
    type: Boolean,
    default: false,
  },
})
const bindIcon = () => {
  return close
}
const store = useStore()
const submit = async () => {
  if (!username.value) {
    ElMessage({
      showClose: true,
      message: '请输入账号',
      type: 'warning',
    })
    return
  }
  if (!psd.value) {
    ElMessage({
      showClose: true,
      message: '请输入密码',
      type: 'warning',
    })
    return
  }
  store.commit('SET_NAME', username.value)
  closeDialog()
}
const reset = () => {
  username.value = ''
  psd.value = ''
}
const closeDialog = () => {
  emit('closeLogin')
}
</script>
<style lang="sass" scoped>
.login
  background-color: rgba(0, 30, 40, 0.78)
  border-radius: 4px
  position: fixed
  left: calc(50% - 160px)
  top: 200px
  width: 320px
  .close-btn
    position: relative
    width: 18px
    height: 18px
    left: 93%
    top: 5px
    cursor: pointer
</style>
