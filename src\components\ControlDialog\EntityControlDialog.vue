<template>
  <!-- 实体控制对话框 -->
  <el-dialog
    v-model="store.state.app.entityControlBoxShow"
    :close-on-click-modal="false"
    title="实体控制"
    width="35%"
    draggable
    top="10vh"
    append-to-body
  >
    <el-row class="controlBox">
      <div class="left">
        <div class="title">
          <span>实体列表</span>
          <el-button :icon="Plus" @click="handleCreate">添加实体</el-button>
        </div>
        <div class="left-content">
          <el-row class="tabs">
            <div
              v-for="item in sideList"
              :key="item.value"
              class="tab"
              :style="{ color: item.color }"
              :class="[{ active: currentSide === item.value }]"
              @click="currentSide = item.value"
            >
              {{ item.label }}
            </div>
          </el-row>
          <template v-if="sideEntities.length">
            <div class="entityList">
              <el-row
                v-for="item in sideEntities"
                :key="item"
                class="entity"
                :title="item"
                :style="{
                  color: sideColor,
                  background: item === currentEntity ? '#0175b8' : '',
                }"
                @click="currentEntity = item"
              >
                <span> {{ item }}</span>
                <el-row>
                  <img
                    class="deleteIcon"
                    src="/image/delete_icon.png"
                    alt=""
                    title="删除"
                    @click="deleteEntity(item)"
                  />
                  <img
                    src="/image/copy_icon.png"
                    alt=""
                    title="复制"
                    @click="copyEntity(item)"
                  />
                </el-row>
              </el-row>
            </div>
          </template>
          <template v-else>
            <div class="emptyBox">
              <Empty />
            </div>
          </template>
        </div>
      </div>
      <!-- 右侧实体创建/编辑表单卡片 -->
      <div class="right">
        <div class="title">任务控制</div>
        <el-form v-if="createFlag" :model="createForm" label-width="60px">
          <!-- 实体名称 -->
          <el-form-item label="名称">
            <el-input
              v-model="createForm.name"
              placeholder="请输入实体名称"
            ></el-input>
          </el-form-item>
          <!-- 实体类型 -->
          <el-form-item label="类型">
            <el-select v-model="createForm.type" placeholder="请选择类型">
              <el-option
                v-for="item in entitiesTypeList"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- 实体阵营 -->
          <el-form-item label="阵营">
            <el-select v-model="createForm.side" placeholder="请选择阵营">
              <el-option label="红方" value="red"></el-option>
              <el-option label="蓝方" value="blue"></el-option>
            </el-select>
          </el-form-item>
          <!-- 实体位置信息 -->
          <el-form-item label="经度">
            <el-input
              v-model.number="createForm.lon"
              type="number"
              placeholder="请输入经度"
            >
              <template #suffix>
                <span>度</span>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="纬度">
            <el-input
              v-model.number="createForm.lat"
              type="number"
              placeholder="请输入纬度"
            >
              <template #suffix>
                <span>度</span>
              </template></el-input
            >
          </el-form-item>
          <el-form-item label="高度">
            <el-input
              v-model.number="createForm.alt"
              type="number"
              placeholder="请输入高度"
            >
              <template #suffix>
                <span>米</span>
              </template></el-input
            >
          </el-form-item>
          <!-- 实体姿态和速度信息 -->
          <el-form-item label="朝向">
            <el-input
              v-model.number="createForm.ori"
              type="number"
              placeholder="请输入朝向"
            >
              <template #suffix>
                <span>度</span>
              </template></el-input
            >
          </el-form-item>
          <el-form-item label="速度">
            <el-input
              v-model.number="createForm.speed"
              type="number"
              placeholder="请输入速度"
            >
              <template #suffix>
                <span>米/秒</span>
              </template></el-input
            >
          </el-form-item>
          <!-- 表单操作按钮 -->
          <el-form-item>
            <el-button type="primary" @click="createEntity">发送</el-button>
            <el-button @click="resetForm">清除</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-row>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons'
import { useStore } from 'vuex'
import Empty from '@/components/Empty.vue'
import * as Api from '@/api/dt'
const store = useStore()

// 组件属性定义
const props = defineProps({
  // 实体列表数据
  entitiesList: {
    type: Object,
    default: [],
  },
  // 实体类型列表
  entitiesTypeList: {
    type: Object,
    default: [],
  },
})
const sideList = [
  {
    label: '红方',
    value: 'red',
    color: '#fe7f7f',
  },
  {
    label: '蓝方',
    value: 'blue',
    color: '#00a4c5',
  },
]
const currentSide = ref('red')
const sideEntities = computed(() => {
  return props.entitiesList
    .filter(i => i.si === currentSide.value)
    .map(i => i.na)
})
const sideColor = computed(() => {
  return sideList.find(i => i.value === currentSide.value)?.color || '#fff'
})
// 选中实体
const currentEntity = ref('')

// 当前激活的标签页名称（红方/蓝方）
const activeName = ref('red')
// 控制创建表单的显示状态
const createFlag = ref(false)
// 创建实体的表单数据
const createForm = reactive({
  name: '', // 名称
  type: '', // 类型
  side: currentSide.value, // 阵营
  lon: 0, // 经度
  lat: 0, // 纬度
  alt: 0, // 高度
  ori: 0, // 朝向
  speed: 0, // 速度
})

// 监听标签页切换，更新表单中的阵营值
watch(
  () => currentSide.value,
  () => {
    createForm.side = currentSide.value
  }
)

// 监听对话框显示状态，重置表单
watch(
  () => store.state.app.entityControlBoxShow,
  val => {
    if (!val) return
    createFlag.value = false
    resetForm()
  }
)

/**
 * 处理新建实体按钮点击事件
 */
const handleCreate = () => {
  resetForm()
  createFlag.value = true
}

/**
 * 重置表单数据
 */
const resetForm = () => {
  createForm.name = ''
  createForm.type = ''
  createForm.side = currentSide.value
  createForm.lon = 0
  createForm.lat = 0
  createForm.alt = 0
  createForm.ori = 0
  createForm.speed = 0
}

/**
 * 创建新实体
 * 验证表单数据并发送创建请求
 */
const createEntity = async () => {
  // 表单验证
  if (!createForm.name) {
    ElMessage.warning('请输入实体名称')
    return
  }
  if (!createForm.type) {
    ElMessage.warning('请选择实体类型')
    return
  }

  // 确认创建
  ElMessageBox.confirm('确定要创建该实体吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        // 发送创建实体请求
        await Api.sendEventApi({
          type: 'ExecuteScript',
          content: {
            platform_name: '',
            processor_name: '',
            script_args: [
              createForm.name,
              createForm.type,
              createForm.side,
              createForm.lon || 0,
              createForm.lat || 0,
              createForm.alt || 0,
              createForm.ori || 0,
              createForm.speed || 0,
            ],
            script_name: 'AddPlatform',
            type: 3,
          },
        })
        ElMessage.success('创建成功')
      } catch (error) {
        ElMessage.error('创建失败')
      }
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    })
}

/**
 * 复制实体
 * 根据实体名称获取实体信息并填充到表单中
 * @param na 实体名称
 */
const copyEntity = (na: string) => {
  // 从viewer.entities中获取实体
  const entity = window.viewer.entities.getById(na)

  if (!entity) {
    ElMessage.warning(`未找到名称为 ${na} 的实体`)
    return
  }

  // 获取实体的属性
  const properties = entity.properties
  if (!properties) {
    ElMessage.warning(`实体 ${na} 没有属性信息`)
    return
  }

  // 将实体信息填充到表单中
  createForm.name = na + '_copy' // 复制的实体名称添加后缀
  createForm.type = properties.ty?.getValue() || properties.type?.getValue()
  createForm.side = properties.si?.getValue()
  createForm.lon = properties.lo?.getValue()
  createForm.lat = properties.la?.getValue()
  createForm.alt = properties.al?.getValue()
  // createForm.ori = properties.ya?.getValue()
  createForm.ori = 0
  createForm.speed = properties.sp?.getValue()

  // 显示表单
  createFlag.value = true
}

/**
 * 删除实体
 * 发送删除实体请求
 * @param na 实体名称
 */
const deleteEntity = (na: string) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    // 发送删除实体请求
    await Api.sendEventApi({
      type: 'ExecuteScript',
      content: {
        platform_name: '',
        processor_name: '',
        script_args: [
          na, //实体名称
        ],
        script_name: 'DelPlatform',
        type: 3,
      },
    })
    ElMessage.success('删除成功')
  })
}
</script>

<style lang="scss" scoped>
.controlBox {
  height: 450px;
  /* 左侧卡片样式 */
  .left {
    height: 100%;
    width: 40%;
    border-right: 1px solid var(--app-border-color);
    .title {
      height: 34px;
      background-color: #145d89;
      font-size: 16px;
      padding: 0 10px;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .el-button {
        height: 28px;
      }
    }
    .left-content {
      padding-right: 10px;
      height: calc(100% - 34px - 15px);
      .tabs {
        .tab {
          width: 87px;
          height: 30px;
          line-height: 30px;
          background: #033a5a;
          border: solid 1px var(--app-border-color);
          border-bottom: none;
          cursor: pointer;
          text-align: center;
          margin-right: 10px;
          border-top-left-radius: 2px;
          border-top-right-radius: 2px;
          font-weight: 600;
        }
        .active {
          background: #125d8d;
        }
      }
      .entityList {
        height: calc(100% - 30px - 10px);
        border: 1px solid var(--app-border-color);
        overflow-y: auto;
        padding: 5px 10px;
        .entity {
          padding-left: 5px;
          cursor: pointer;
          line-height: 28px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          justify-content: space-between;
          align-items: center;
          .deleteIcon {
            margin-right: 5px;
          }
          img {
            display: none;
          }
        }
        .entity:hover {
          background: #015586;
          img {
            display: block;
          }
        }
      }
      .emptyBox {
        padding-top: 50px;
        height: calc(100% - 30px - 10px);
        border: 1px solid var(--app-border-color);
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
  }
  .right {
    width: 60%;
    .title {
      line-height: 34px;
      background-color: #145d89;
      font-size: 16px;
      padding-left: 10px;
      margin-bottom: 15px;
    }
    :deep(.el-form) {
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
