export interface AppState {
  widgetsList: any[]
  toolsList: any[]
  openList: string[]
  ifScenarioShow: boolean
  ifLogBoxShow: boolean
  entityListShow: boolean
  effectBoxShow: boolean
  EntityInfo: boolean
  globalEffectBoxShow: boolean
  taskControlBoxShow: boolean
  entityControlBoxShow: boolean
}
export const state: AppState = {
  widgetsList: [],
  toolsList: [],
  openList: [],
  ifScenarioShow: true,
  ifLogBoxShow: true,
  entityListShow: true,
  effectBoxShow: true,
  EntityInfo: true,
  globalEffectBoxShow: false,
  taskControlBoxShow: false,
  entityControlBoxShow: false,
}
