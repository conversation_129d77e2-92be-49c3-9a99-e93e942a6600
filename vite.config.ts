/*
 * @Author: 老范
 * @Date: 2025-03-20 17:50:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-03-31 10:49:45
 * @Description: 请填写简介
 */
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { resolve } from 'path'
import viteSvgIcons from 'vite-plugin-svg-icons'

// https://vitejs.dev/config/
export default {
  plugins: [
    vue(),
    viteSvgIcons({
      iconDirs: [path.resolve(process.cwd(), './src/icons')],
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  // base: './',
  server: {
    hmr: true,
    host: '0.0.0.0',
    port: 3001,
    open: false,
  },
}

// ({ mode }) => ({
//   base: './',
//   host:3011,
//   open:true,
//   plugins: [
//     vue(),
//     viteSvgIcons({
//       iconDirs: [path.resolve(process.cwd(), './src/icons')],
//     }),
//   ],
//   resolve: {
//     alias: {
//       '@': resolve(__dirname, './src'),
//     },
//   },
// })
