<template>
  <el-row class="cardConfigBox">
    <el-row class="left">
      <el-row
        class="item"
        @click="
          store.commit('SET_ScenarioShow', !store.state.app.ifScenarioShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.ifScenarioShow" /><Hide v-else
        /></el-icon>
        想定信息
      </el-row>
      <el-row
        class="item"
        @click="store.commit('SET_LogBoxShow', !store.state.app.ifLogBoxShow)"
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.ifLogBoxShow" /><Hide v-else
        /></el-icon>
        日志
      </el-row>
      <el-row
        class="item"
        @click="
          store.commit('SET_EntityListBoxShow', !store.state.app.entityListShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.entityListShow" /><Hide v-else
        /></el-icon>
        实体列表
      </el-row>
      <el-row
        class="item"
        @click="store.commit('SET_EntityInfoShow', !store.state.app.EntityInfo)"
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.EntityInfo" /><Hide v-else
        /></el-icon>
        实体信息
      </el-row>
      <el-row
        class="item"
        @click="
          store.commit('SET_EffectBoxShow', !store.state.app.effectBoxShow)
        "
      >
        <el-icon class="icon"
          ><View v-if="store.state.app.effectBoxShow" /><Hide v-else
        /></el-icon>
        实体配置
      </el-row>
    </el-row>
    <el-row class="right">
      <el-row class="item">
        <div class="scale-container">
          <div class="map-scale-bar" :style="{ width: mapScale.barWidth + 'px' }">
            <span>{{ mapScale.distanceLabel }}</span>
          </div>
        </div>
      </el-row>
      <el-row class="item">
        <div>经度：</div>
        <div class="value">{{ longitude }}</div>
      </el-row>
      <el-row class="item">
        <div>维度：</div>
        <div class="value">{{ latitude }}</div>
      </el-row>
    </el-row>
  </el-row>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { ref, onMounted, onUnmounted } from 'vue'

const store = useStore()

// 经纬度数据
const longitude = ref('-')
const latitude = ref('-')

// 比例尺数据
const mapScale = ref({
  _lastLegendUpdate: 0,
  distanceLabel: '0 km',
  barWidth: 50,
})

// 鼠标移动事件处理器
let mouseHandler: any = null
let scaleUpdateHandler: any = null

// 比例尺计算相关
const distances = [
  1, 2, 3, 5, 10, 20, 30, 50, 100, 200, 300, 500, 1e3, 2e3, 3e3, 5e3, 1e4, 2e4,
  3e4, 5e4, 1e5, 2e5, 3e5, 5e5, 1e6, 2e6, 3e6, 5e6, 1e7, 2e7, 3e7, 5e7,
]
const geodesic = new Cesium.EllipsoidGeodesic()

// 更新比例尺
const updateDistanceLegend = (viewModel: any, scene: any) => {
  const now = (Cesium as any).getTimestamp()
  if (now < viewModel._lastLegendUpdate + 250) {
    return
  }
  if (!scene || !scene.canvas) {
    return
  }
  viewModel._lastLegendUpdate = now
  const width = scene.canvas.clientWidth
  const height = scene.canvas.clientHeight
  const left = scene.camera.getPickRay(
    new Cesium.Cartesian2((width / 2) | 0, height - 1)
  )
  const right = scene.camera.getPickRay(
    new Cesium.Cartesian2((1 + width / 2) | 0, height - 1)
  )
  const globe = scene.globe
  const leftPosition = globe.pick(left, scene)
  const rightPosition = globe.pick(right, scene)
  if (!Cesium.defined(leftPosition) || !Cesium.defined(rightPosition)) {
    viewModel.barWidth = 50
    viewModel.distanceLabel = '0 km'
    return
  }
  const leftCartographic = globe.ellipsoid.cartesianToCartographic(leftPosition)
  const rightCartographic = globe.ellipsoid.cartesianToCartographic(rightPosition)
  geodesic.setEndPoints(leftCartographic, rightCartographic)
  const pixelDistance = geodesic.surfaceDistance
  const maxBarWidth = 50 // 固定宽度50px
  let distance: number | undefined
  for (let i = distances.length - 1; distance === undefined && i >= 0; --i) {
    if (distances[i] / pixelDistance < maxBarWidth) {
      distance = distances[i]
    }
  }
  if (distance !== undefined) {
    let label
    if (distance >= 1e3) {
      label = (distance / 1e3).toString() + ' km'
    } else {
      label = distance.toString() + ' m'
    }
    viewModel.barWidth = (distance / pixelDistance) | 0
    viewModel.distanceLabel = label
  } else {
    viewModel.barWidth = 50
    viewModel.distanceLabel = '0 km'
  }
}

onMounted(() => {
  // 等待viewer初始化
  const initInterval = setInterval(() => {
    if (window.viewer && window.GV) {
      clearInterval(initInterval)

      // 初始化鼠标移动监听
      mouseHandler = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas)
      mouseHandler.setInputAction((event: any) => {
        const position = event
        // 根据屏幕坐标获取坐标位置
        const point = window.GV.GeoPoint.fromScreen(
          position.endPosition.x,
          position.endPosition.y,
          window.viewer
        )
        if (point) {
          longitude.value = point.lon.toFixed(6)
          latitude.value = point.lat.toFixed(6)
        }else{
          longitude.value = '-'
          latitude.value = '-'
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

      // 初始化比例尺更新
      scaleUpdateHandler = () => {
        updateDistanceLegend(mapScale.value, window.viewer.scene)
      }
      window.viewer.scene.postRender.addEventListener(scaleUpdateHandler)
    }
  }, 100)
})

onUnmounted(() => {
  // 清理事件监听器
  if (mouseHandler) {
    mouseHandler.destroy()
    mouseHandler = null
  }
  if (scaleUpdateHandler && window.viewer) {
    window.viewer.scene.postRender.removeEventListener(scaleUpdateHandler)
    scaleUpdateHandler = null
  }
})
</script>

<style lang="less" scoped>
.cardConfigBox {
  position: fixed;
  bottom: 1px;
  z-index: 1;
  width: 100%;
  height: 32px;
  font-size: 15px;
  background-color: #002f49;
  border-top: 1px solid var(--app-border-color);
  padding: 0 20px;
  justify-content: space-between;
  .left {
    height: 100%;
    .item {
      cursor: pointer;
      align-items: center;
      margin-right: 20px;
      .icon {
        margin-right: 5px;
      }
    }
    .item:hover {
      color: #339af0;
    }
  }
  .right {
    height: 100%;
    .item {
      margin-right: 20px;
      align-items: center;
      .value:
    }
    .scale-container {
      display: flex;
      align-items: center;
      .map-scale-bar {
        position: relative;
        height: 4px;
        background-color: #339af0;
        border: 1px solid #339af0;
        border-radius: 2px;
        min-width: 30px;
        max-width: 80px;
        span {
          position: absolute;
          top: -20px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 12px;
          color: #fff;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
