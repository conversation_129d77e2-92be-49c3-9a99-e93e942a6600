<template>
  <div class="flex title justify-center items-center bg-top">
    <div class="flex items-center">
      <img src="/logo.png" alt="" class="imgs" />
      <h2 class="text-5xl text-white">{{ props.title }}</h2>
    </div>

    <times
      v-for="(item, index) in Times"
      :key="index"
      class="mr-2"
      :timeTitle="item.name"
      :option="item"
      color="orange"
    />
    <!-- <times :timeTitle="timeTitle.time" :date="getDate" /> -->
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue'
import times from './time.vue'
import { formatYearMontDay } from '@/utils/util'

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
})
const Times = window.GVJ.Times

const getDate = computed((): string => {
  return formatYearMontDay(new Date())
})

const timeTitle = {
  time: '作战时',
  curTime: '军用标准时',
}
</script>
<style scoped>
.title {
  width: 998px;
  background: url('../../assets/images/backmiddle.png') no-repeat;
  background-position-y: -10px;
}
.imgs {
  width: 65px;
}
h2 {
  margin-right: 5px;
}
</style>
