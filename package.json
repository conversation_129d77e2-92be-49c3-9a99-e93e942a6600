{"name": "system-vue3", "version": "0.0.0", "scripts": {"start": "vite", "dev": "vite --host", "build": "vite build", "serve": "vite preview"}, "dependencies": {"@element-plus/icons": "^0.0.11", "echarts": "^5.6.0", "element-plus": "^2.9.1", "js-cookie": "^3.0.0", "lodash-es": "^4.17.21", "vue": "^3.0.5", "vue-router": "^4.0.11", "vuex": "^4.0.2"}, "devDependencies": {"@types/node": "^16.6.0", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "@vitejs/plugin-vue": "^1.3.0", "@vue/compiler-sfc": "^3.0.5", "autoprefixer": "^10.3.1", "axios": "^0.21.1", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "less": "^4.3.0", "node-sass": "^6.0.1", "nprogress": "^0.2.0", "postcss": "^8.3.6", "prettier": "^2.3.2", "sass": "^1.37.5", "sass-loader": "^10.2.0", "tailwindcss": "^2.2.7", "typescript": "^4.3.2", "vite": "^2.4.4", "vite-plugin-svg-icons": "^1.0.4", "vue-tsc": "^0.2.2"}}