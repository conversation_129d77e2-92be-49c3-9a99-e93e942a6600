@font-face {
  font-family: "gv-iconfont";
  src: url('gv-iconfont.eot?t=1548217875547'); /* IE9*/
  src: url('gv-iconfont.eot?t=1548217875547#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url("gv-iconfont.woff2?t=1548217875547") format("woff2"),
  url("gv-iconfont.woff?t=1548217875547") format("woff"),
  url('gv-iconfont.ttf?t=1548217875547') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('gv-iconfont.svg?t=1548217875547#gv-iconfont') format('svg'); /* iOS 4.1- */
}

[class^="gv-iconfont-"], [class*=" gv-iconfont-"] {
  font-family: 'gv-iconfont' !important;
  font-size:16px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


.gv-iconfont-3d:before { content: "\ea01"; }
.gv-iconfont-三维漫游:before { content: "\ea02"; }
.gv-iconfont-位置:before { content: "\ea03"; }
.gv-iconfont-关闭-实:before { content: "\ea04"; }
.gv-iconfont-关闭-面:before { content: "\ea05"; }
.gv-iconfont-关闭:before { content: "\ea06"; }
.gv-iconfont-卫星:before { content: "\ea07"; }
.gv-iconfont-卷帘:before { content: "\ea08"; }
.gv-iconfont-卷帘对比:before { content: "\ea09"; }
.gv-iconfont-图层:before { content: "\ea0a"; }
.gv-iconfont-坐标:before { content: "\ea0b"; }
.gv-iconfont-基础设施:before { content: "\ea0c"; }
.gv-iconfont-复位:before { content: "\ea0d"; }
.gv-iconfont-多边形:before { content: "\ea0e"; }
.gv-iconfont-截图:before { content: "\ea0f"; }
.gv-iconfont-拼图:before { content: "\ea10"; }
.gv-iconfont-搜索:before { content: "\ea11"; }
.gv-iconfont-时间线:before { content: "\ea12"; }
.gv-iconfont-枪支:before { content: "\ea13"; }
.gv-iconfont-标绘:before { content: "\ea14"; }
.gv-iconfont-武器装备设置:before { content: "\ea15"; }
.gv-iconfont-海运:before { content: "\ea16"; }
.gv-iconfont-清空:before { content: "\ea17"; }
.gv-iconfont-演示:before { content: "\ea18"; }
.gv-iconfont-漫游:before { content: "\ea19"; }
.gv-iconfont-照相:before { content: "\ea1a"; }
.gv-iconfont-燕尾箭头:before { content: "\ea1b"; }
.gv-iconfont-用户:before { content: "\ea1c"; }
.gv-iconfont-画板主题:before { content: "\ea1d"; }
.gv-iconfont-空间量测:before { content: "\ea1e"; }
.gv-iconfont-绘制多边形:before { content: "\ea1f"; }
.gv-iconfont-罗盘_compass83:before { content: "\ea20"; }
.gv-iconfont-罗盘_compass84:before { content: "\ea21"; }
.gv-iconfont-行为态势:before { content: "\ea22"; }
.gv-iconfont-部队管理:before { content: "\ea23"; }
.gv-iconfont-飞行漫游:before { content: "\ea24"; }
