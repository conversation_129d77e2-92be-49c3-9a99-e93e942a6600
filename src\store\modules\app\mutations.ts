/*
 * @Author: 老范
 * @Date: 2025-05-12 15:42:07
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-05 09:28:12
 * @Description: 请填写简介
 */

import { MutationTree } from 'vuex'
import { AppState } from './state'
import { AppMutationTypes, AppMutationOption } from './mutations-types'

export type Mutations<S = AppState> = {
  [AppMutationTypes.SET_WIDGETSLIST](state: S, list: any[]): void
  [AppMutationTypes.SET_TOOLSLIST](state: S, list: any[]): void
  [AppMutationTypes.CHANGE_TOOLSLIST](state: S, option: AppMutationOption): void
  [AppMutationTypes.CHANGE_WIDGETSLIST](
    state: S,
    option: AppMutationOption
  ): void
  [AppMutationTypes.SET_OPENLIST](state: S, list: string[]): void
}

// export const mutations:MutationTree<AppState>&Mutations = {

export const mutations: MutationTree<AppState> & Mutations = {
  SET_WIDGETSLIST(state: AppState, list: any[]) {
    list.map(ele => {
      ele.state = ele.state || 2
    })
    state.widgetsList = list.map(ele => {
      return {
        name: ele.name,
        state: ele.state,
      }
    })
  },
  SET_TOOLSLIST(state: AppState, list: any[]) {
    list.map(ele => {
      let arr = [0, 1, 2]
      if (arr.indexOf(ele.state) == -1) {
        ele.state = 2
      }
    })
    state.toolsList = list.map(ele => {
      return {
        name: ele.name,
        state: ele.state,
      }
    })
  },
  CHANGE_TOOLSLIST(state: AppState, option: AppMutationOption) {
    state.toolsList.map(ele => {
      ele.state = option.close ? 2 : 1
    })
    if (!option.close) {
      state.toolsList[option.index].state = option.state
    }
  },
  CHANGE_WIDGETSLIST(state: AppState, option: AppMutationOption) {
    state.widgetsList.map(ele => {
      ele.state = 2
    })
    state.widgetsList[option.index].state = option.state
  },
  SET_OPENLIST(state: AppState, list: any[]) {
    state.openList = list
  },
  SET_ScenarioShow(state: AppState, isSHow: boolean) {
    state.ifScenarioShow = isSHow
  },
  SET_LogBoxShow(state: AppState, isSHow: boolean) {
    state.ifLogBoxShow = isSHow
  },
  SET_EntityListBoxShow(state: AppState, isSHow: boolean) {
    state.entityListShow = isSHow
  },
  SET_EffectBoxShow(state: AppState, isSHow: boolean) {
    state.effectBoxShow = isSHow
  },
  SET_EntityInfoShow(state: AppState, isSHow: boolean) {
    state.EntityInfo = isSHow
  },
  SET_GlobalEffectBoxShow(state: AppState, isSHow: boolean) {
    state.globalEffectBoxShow = isSHow
  },
  SET_TaskControlBoxShow(state: AppState, isSHow: boolean) {
    state.taskControlBoxShow = isSHow
  },
  SET_EntityControlBoxShow(state: AppState, isSHow: boolean) {
    state.entityControlBoxShow = isSHow
  },
}
