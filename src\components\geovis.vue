<!--
 * @Author: 老范
 * @Date: 2025-03-20 17:50:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-05 09:29:47
 * @Description: 请填写简介
-->
<template>
  <div id="GEOVISContainer">
    <div class="leftCardBox">
      <ScenarioInfo
        v-show="store.state.app.ifScenarioShow"
        :scenarioInfo="scenarioParams"
      />
      <LogCard
        v-show="store.state.app.ifLogBoxShow"
        ref="logBoxRef"
        @sendMessage="setLogInfo"
      />
    </div>
    <div class="rightCardBox">
      <EntityListCard
        v-show="store.state.app.entityListShow"
        @g-db-click="focusEntity"
        :entitiesList="existEntity"
      />
      <EntityInfoCard v-show="store.state.app.EntityInfo" />
      <EntityConfig v-show="store.state.app.effectBoxShow" />
    </div>
    <CardConfig />
    <img
      class="cameraReset"
      src="/image/camera_reset_icon.png"
      alt=""
      title="重置相机"
      @click="handleCameraReset"
      :style="{ right: store.state.app.effectBoxShow ? '265px' : '7px' }"
    />
    <img
      class="globalSimConfig"
      src="/image/global_sim_config.png"
      alt=""
      title="态势显示配置"
      @click="handleGlobalSimConfig"
      :style="{ right: store.state.app.effectBoxShow ? '265px' : '7px' }"
    />
    <img
      class="fullScreen"
      src="/image/full_screen_icon.png"
      alt=""
      :title="isFullscreen ? '退出全屏' : '全屏'"
      width="22"
      @click="handleFullscreen"
      :style="{ left: store.state.app.ifScenarioShow ? '267px' : '7px' }"
    />
    <el-button
      class="taskControlBtn"
      :style="{ right: store.state.app.entityListShow ? '384px' : '126px' }"
      :disabled="store.state.simulationConfig.taskStatus !== 2"
      @click="handleTaskControl"
      ><img src="/image/task_control_icon.png" alt="" width="15" />
      任务控制</el-button
    >
    <el-button
      class="entityControlBtn"
      :style="{ right: store.state.app.entityListShow ? '265px' : '7px' }"
      :disabled="store.state.simulationConfig.taskStatus !== 2"
      @click="handleEntityControl"
      ><img
        src="/image/entity_control_icon.png"
        alt=""
        width="17"
      />实体控制</el-button
    >
    <GlobalEffectConfigDialog />
    <!-- <scenarioInfo
      v-if="store.state.app.ifScenarioShow"
      ref="scenarioInfoRef"
      @closeBox="closeInfoBox"
      :scenarioInfo="scenarioParams"
    ></scenarioInfo> -->
    <!-- <logBox
      v-show="store.state.app.ifLogBoxShow"
      ref="logBoxRef"
      @closeBox="closeLogBox"
      @sendMessage="setLogInfo"
    ></logBox> -->

    <!-- <entityList
      v-show="store.state.app.entityListShow"
      ref="entityListRef"
      @closeBox="closeListBox"
      @focusEntity="focusEntity"
      :entitiesList="existEntity"
    ></entityList>
    <entityInfo
      v-if="store.state.app.entityListShow"
      @closeBox="closeEntityInfo"
      :id="currentEntityId"
    ></entityInfo>
    <entityConfig
      v-if="store.state.app.effectBoxShow"
      @closeBox="closeEffectControlBox"
      :id="currentEntityId"
    ></entityConfig> -->
    <!-- <globalEffectConfig
      v-show="store.state.app.globalEffectBoxShow"
      @closeBox="closeGlobalEffectBox"
    ></globalEffectConfig> -->
    <EntityControlDialog
      :entitiesList="existEntity"
      :entitiesTypeList="entitiesTypeList"
    />
    <TaskControlDialog :entitiesList="existEntity" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, provide } from 'vue'
import { useStore } from 'vuex'
// import scenarioInfo from '@/components/leftBox/scenarioInfo.vue'
// import logBox from '@/components/leftBox/logBox.vue'
// import entityList from '@/components/rightBox/entityList.vue'
// import entityInfo from '@/components/rightBox/entityInfo.vue'
// import entityConfig from '@/components/rightBox/entityConfig.vue'
// import globalEffectConfig from '@/components/rightBox/globalEffectConfig.vue'
import EntityControlDialog from '@/components/ControlDialog/EntityControlDialog.vue'
import TaskControlDialog from '@/components/ControlDialog/TaskControlDialog.vue'
import GlobalEffectConfigDialog from '@/components/ControlDialog/GlobalEffectConfigDialog.vue'
import ScenarioInfo from '@/components/SimCard/ScenarioInfo.vue'
import LogCard from '@/components/SimCard/LogCard.vue'
import EntityListCard from '@/components/SimCard/EntityListCard.vue'
import EntityInfoCard from '@/components/SimCard/EntityInfoCard.vue'
import EntityConfig from '@/components/SimCard/EntityConfig.vue'
import CardConfig from '@/components/SimCard/CardConfig.vue'
import { ModelManager } from '@/utils/modelControler'
import { EffectManager } from '@/utils/effectManager'

const GV = window.GV
const store = useStore()

const scenarioParams = reactive({
  scenarioDescribe: '',
  scenarioName: '',
  modeText: '',
  startTime: '',
  realTime: 0,
})
const createView = () => {
  const viewer = new GV.GeoCanvas('GEOVISContainer', {
    baseLayerPicker: false,
    shouldAnimate: true, //动画开启
    imageryProvider: new Cesium.WebMapTileServiceImageryProvider({
      url: `${window.GVJ.URLS.icenterServer}:8210/service/wmts?layer=Global_Image-JPEG-4326`,
      layer: 'Global_Image-JPEG-4326',
      style: 'default',
      format: 'image/JPEG',
      tileMatrixSetID: 'EPSG:4326',
      tilingScheme: new Cesium.GeographicTilingScheme(),
    }),
  })
  return viewer
}
const showEntityInfo = ref(false)
const isFullscreen = ref(false)
const currentEntityId = ref('')
const currentSide = ref('red')
/** 共享当前选中的实体id */
provide('currentEntityId', currentEntityId)
provide('currentSide', currentSide)
const existEntity = ref([])
const entitiesTypeList = ref([])
const logBoxRef = ref()
const entityListRef = ref()
const scenarioInfoRef = ref()
const onEntityClick = (entityId: string) => {
  currentEntityId.value = entityId
  window.viewer.trackedEntity = window.viewer.entities.getById(entityId)
}

/** 重置相机 */
const handleCameraReset = () => {
  // 北京坐标: 经度 116.4074, 纬度 39.9042
  // 高度设置为 20000000 米 (2万公里) 以便能看到整个地球
  window.viewer.iCamera.flyTo(
    new GV.ViewPoint(116.4074, 39.9042, 20000000, 0, -90, 0),
    0 // 飞行时间
  )
}

/** 态势显示配置 */
const handleGlobalSimConfig = () => {
  store.commit('SET_GlobalEffectBoxShow', true)
}

/** 实体控制 */
const handleEntityControl = () => {
  store.commit('SET_EntityControlBoxShow', true)
}

/** 任务控制 */
const handleTaskControl = () => {
  store.commit('SET_TaskControlBoxShow', true)
}

const closeInfoBox = () => {
  store.commit('SET_ScenarioShow', false)
}
const closeLogBox = () => {
  store.commit('SET_LogBoxShow', false)
}
const closeListBox = () => {
  store.commit('SET_EntityListBoxShow', false)
}
const closeEffectControlBox = () => {
  store.commit('SET_EffectBoxShow', false)
}
const closeGlobalEffectBox = () => {
  store.commit('SET_GlobalEffectBoxShow', false)
}

/** 跟踪实体 */
const focusEntity = entityId => {
  window.viewer.trackedEntity = window.viewer.entities.getById(entityId)
}
const closeEntityInfo = () => {
  store.commit('SET_EntityInfoShow', false)
  currentEntityId.value = ''
  window.viewer.trackedEntity = undefined
  entityListRef.value.clearHighLight()
}

/** 追加日志 */
const setLogInfo = (message: string) => {
  logBoxRef.value.sendMessage(message)
}

/** 相机飞行至场景上空 */
const viewerToAnodic = (maxDistance: number, center: number[]) => {
  const scale = (maxDistance * 1000) / 2
  const earthRadius = 6371000
  const alt =
    Math.sqrt(3) * scale +
    Math.sqrt(earthRadius * earthRadius - scale * scale) -
    earthRadius

  // 动态偏移高度，例如增加场景 scale 的 2.5倍
  const offset = scale * 2.5
  console.log(scale * 0.2)

  window.viewer.iCamera.flyTo(new GV.ViewPoint(...center, alt + offset), 0)
}

/** 全屏 */
const handleFullscreen = () => {
  const dom = document.getElementById('GEOVISContainer')
  if (!dom) return
  if (!document.fullscreenElement) dom.requestFullscreen()
  else document.exitFullscreen()
  isFullscreen.value = !document.fullscreenElement
}
// 声明WebSocket引用变量
let wsModelData: WebSocket | null = null
let wsSystemInfo: WebSocket | null = null
let wsRealtimeData: WebSocket | null = null
let wsModelState: WebSocket | null = null
let wsPlatformType: WebSocket | null = null
let routeLines: Cesium.Entity[] = []

const initWebsocket = () => {
  const effectManager = new EffectManager(
    window.viewer,
    `${(window as any).GVJ.URLS.socketServer}api/v1/data-socket/getEvents`,
    setLogInfo
  )
  window.effectManager = effectManager
  const modelManager = new ModelManager(
    window.viewer,
    effectManager,
    {
      maxTrailPoints: 200,
      trailColor: Cesium.Color.RED,
    },
    onEntityClick
  )
  window.modelManager = modelManager

  // 初始化各个WebSocket连接
  initSystemInfoSocket()
  initModelDataSocket()
  initRealtimeDataSocket()
  initModelStateSocket()
  initPlatformTypeSocket()
}

// 初始化模型数据WebSocket - 获取初始模型和路线数据
const initModelDataSocket = () => {
  let isMounted = true
  wsModelData = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getModelData?pushType=init`
  )

  wsModelData.onmessage = event => {
    const data: any = JSON.parse(event.data)
    scenarioParams.scenarioDescribe = data.description
    scenarioParams.scenarioName = data.name
    scenarioParams.startTime = data.startTime

    // 初始化模型
    data.init_model.forEach(item => {
      existEntity.value.push(item)
      window.modelManager.createNewModel(item)
      window.modelManager.trailPrimitive.addTrail(item.na, [
        { lo: item.lo, la: item.la, al: item.al | 0 },
        { lo: item.lo, la: item.la, al: item.al | 0 },
      ])
    })

    // 预设航路
    data.routes.forEach(route => {
      const routeAry = route.parameters.flat()
      const catrePosition = Cesium.Cartesian3.fromDegreesArrayHeights(routeAry)

      const spline = new Cesium.CatmullRomSpline({
        times: catrePosition.map(
          (_, index) => index / (catrePosition.length - 1)
        ),
        points: catrePosition,
      })
      const smoothPosition: Cesium.Cartesian3[] = []
      const sampleCount = 10
      for (let index = 0; index < sampleCount; index++) {
        const time = index / sampleCount
        smoothPosition.push(spline.evaluate(time))
      }
      const line = window.viewer.entities.add({
        id: `${route.type}_initRoute`,
        name: '线',
        polyline: {
          width: 1.0,
          positions: catrePosition,
          material: Cesium.Color.YELLOW.withAlpha(0.7),
        },
      })
      routeLines.push(line)
    })
    if (
      store.state.simulationConfig.taskStatus === 1 ||
      (store.state.simulationConfig.taskStatus === 2 && isMounted)
    ) {
      viewerToAnodic(data.cameraConfig.maxDistance, data.cameraConfig.center)
      isMounted = false
    }
  }
}

// 初始化系统信息WebSocket - 获取系统状态信息
const initSystemInfoSocket = () => {
  wsSystemInfo = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getSysInfo?type=init`
  )

  wsSystemInfo.onmessage = event => {
    const data: any = JSON.parse(event.data)
    scenarioParams.modeText = `${data.mode ? '回放' : '推演'}模式-${
      data.simulateMode ? '帧' : '事件'
    }模式`

    // 更新全局taskStatus变量
    store.commit('SET_TASK_STATUS', data.taskStatus)
    console.log('任务状态已更新:', store.state.simulationConfig.taskStatus)

    if (data.taskStatus === 0) {
      // 清空
      window.modelManager.reset()
      // 清除路线
      routeLines.forEach(line => {
        window.viewer.entities.remove(line)
      })
      routeLines = []
      entitiesTypeList.value = []
    } else if (data.taskStatus === 4) {
      existEntity.value.length = 0
      logBoxRef.value.clearList()
      currentEntityId.value = ''
    }
  }
}

// 初始化实时数据WebSocket - 获取模型实时位置更新
const initRealtimeDataSocket = () => {
  wsRealtimeData = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getModelData?pushType=realtime`
  )

  wsRealtimeData.onmessage = event => {
    const data: any[] = JSON.parse(event.data)
    window.modelManager.batchUpdate(data.up, data.ts)
    scenarioParams.realTime = data.ts
    if (scenarioInfoRef.value) {
      scenarioInfoRef.value.getRealTime(data.ts)
    }
    // 新增：同步simTimestamp到vuex
    store.commit('SET_SIM_TIMESTAMP', data.ts)
  }
}

// 初始化模型状态WebSocket - 获取模型销毁等状态变化
const initModelStateSocket = () => {
  wsModelState = new WebSocket(
    `${(window as any).GVJ.URLS.socketServer}api/v1/data-socket/getModelState`
  )

  wsModelState.onmessage = event => {
    const data: any = JSON.parse(event.data)
    data.events.forEach(item => {
      if (item.param.type == 'disappear') {
        // 延迟销毁模型（爆炸效果后）
        setTimeout(() => {
          window.modelManager.destroyModel(item.name)
        }, 2000)
      } else {
        const effect = {
          config: { modelId: item.name },
        }
        // 从实体列表中移除
        existEntity.value = existEntity.value.filter(existItem => {
          return existItem.na !== item.name
        })
        // 渲染爆炸效果
        window.modelManager.effectManager.renderExplosionEffect(effect)
        // 销毁模型
        window.modelManager.destroyModel(item.name)
      }
    })
  }
}

// 初始化平台类型WebSocket - 获取实体类型信息
const initPlatformTypeSocket = () => {
  wsPlatformType = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getModelData?pushType=platformType`
  )

  wsPlatformType.onmessage = event => {
    const data: any = JSON.parse(event.data)
    data.forEach(i => {
      const blueType = Object.keys(i.blueSide.types)
      const redType = Object.keys(i.redSide.types)
      const typeSet = new Set([
        ...entitiesTypeList.value,
        ...blueType,
        ...redType,
      ])
      entitiesTypeList.value = Array.from(typeSet)
    })
  }
}

// 关闭所有WebSocket连接
const closeAllWebSockets = () => {
  ;[
    wsModelData,
    wsSystemInfo,
    wsRealtimeData,
    wsModelState,
    wsPlatformType,
  ].forEach(ws => {
    if (ws && ws.readyState !== WebSocket.CLOSED) {
      ws.close()
    }
  })

  // 重置引用
  wsModelData = null
  wsSystemInfo = null
  wsRealtimeData = null
  wsModelState = null
  wsPlatformType = null
}

// 组件卸载时关闭所有WebSocket连接
onUnmounted(() => {
  closeAllWebSockets()
})
onMounted(async () => {
  window.viewer = createView()
  // 开启帧率
  // window.viewer.scene.debugShowFramesPerSecond = true
  initWebsocket()
  // window.GVJ.createdePluginManager(window.viewer, [])
  // 此时添加常用列表
  // let arr = window.GVJ.allPlugin.tools.filter(ele => {
  //   return ele.showMenu
  // })
  // arr = arr.map(ele => {
  //   return {
  //     name: ele.plugName,
  //   }
  // })
  // store.commit('SET_TOOLSLIST', arr)
  // // 右下角
  // arr = window.GVJ.allPlugin.Widgets.filter(ele => {
  //   return ele.showMenu
  // })
  // arr = arr.map(ele => {
  //   return {
  //     name: ele.plugName,
  //   }
  // })
  // store.commit('SET_WIDGETSLIST', arr)
})
</script>

<style lang="less" scoped>
#GEOVISContainer {
  width: 100%;
  height: 100%;
  position: absolute;
  .leftCardBox,
  .rightCardBox {
    padding-top: 5px;
    position: absolute;
    width: 260px;
    height: calc(100% - 36px);
    top: 0;
    z-index: 1;
    font-size: 16px;
  }
  .leftCardBox {
    left: 0;
  }
  .rightCardBox {
    right: 0;
  }
  .cameraReset,
  .globalSimConfig {
    position: absolute;
    right: 265px;
    bottom: 74px;
    z-index: 1;
    cursor: pointer;
  }
  .globalSimConfig {
    bottom: 36px;
  }
  .entityControlBtn {
    top: 5px;
    right: 265px;
  }
  .fullScreen {
    position: absolute;
    top: 5px;
    z-index: 1;
    cursor: pointer;
  }
  .taskControlBtn {
    top: 5px;
    right: 384px;
  }
  .entityControlBtn,
  .taskControlBtn {
    position: absolute;
    z-index: 1;
    img {
      margin-right: 5px;
    }
  }
}
</style>
