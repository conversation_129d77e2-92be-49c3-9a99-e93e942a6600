<template>
  <ResizableDialog
    class="drag-dialog"
    v-if="!!pluginDialogFlag"
    :title="title"
    position="left-top"
    style="width: 230px; height: calc(100vh / 1.5); overflow-y: hidden"
    body-style="padding:0;"
    @close="closeDialog"
    @click="showcy = false"
  >
    <el-collapse>
      <el-collapse-item>
        <template #title>
          通用插件
          <el-tooltip
            class="item"
            effect="dark"
            placement="top-start"
            content="右键将插件从常用列表新增或删除"
            >
             
            <i class="header-icon el-icon-info"></i
          ></el-tooltip>
        </template>
        <!-- <div
          v-for="(item, index) in menuContent"
          :key="index"
          class="flex-svg-content"
          :style="{
            'background-color': showColor(item) ? '#0b82a1 !important' : '',
            'border-bottom': showColor(item) ? '1px solid skyblue' : '',
          }"
          @click="pluginClick(item)"
          @contextmenu.prevent="addmenuflag($event, item)"
        >
          <div>
            <img
              :src="getimg(item)"
              :class="{ borders: getborder(item) }"
              class="shortcuts-el"
            />
          </div>
          <div>{{ item.plugName }}</div>
        </div> -->
      </el-collapse-item>
      <!-- <el-collapse-item title="二次开发">
        <template #title>
          二次开发<i class="header-icon el-icon-info"></i>
        </template>
      </el-collapse-item>
      <el-collapse-item title="项目支撑">
        <template #title>
          项目支撑<i class="header-icon el-icon-info"></i>
        </template>
      </el-collapse-item>
      <el-collapse-item title="未分类">
        <template #title>
          未分类<i class="header-icon el-icon-info"></i>
        </template>
      </el-collapse-item> -->
    </el-collapse>
    <div
      class="addcy"
      v-if="showcy"
      :style="{ top: top + 'px' }"
      @click="addToCY"
    >
      {{ text }}
    </div>
  </ResizableDialog>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch, defineEmits } from 'vue'
import ResizableDialog from '../resizeDialog/index.vue'
import { useStore } from 'vuex'
const dialogVisible = ref(true)
const menuInfo = ref({ flag: false, row: {} })
const props = defineProps({
  pluginDialogFlag: {
    type: Boolean,
    required: true,
    default: false,
  },
  title: {
    type: String,
    default: '插件列表',
  },
})
const text = ref<string>('添加到常用列表')
const store = useStore()
const emit = defineEmits(['closepluginDialogFlag'])
// 两个合并
// const menuContent = ref<any[]>(
//   window.GVJ.allPlugin.tools.concat(window.GVJ.allPlugin.Widgets)
// )
// 选中颜色判断
const showColor = data => {
  return store.state.app.openList.indexOf(data.plugName) !== -1
}
const pluginClick = (plugin: any) => {
  // 判断是否是tools类型
  let tools = window.dePluginManager.Tools.find(
    (ele: any) => ele.name == plugin.plugName
  )
  let list = store.state.app.openList
  if (tools) {
    // 插件切换之前 添加状态
    // 注意：插件只显示一个  所以需要将所有工具类的插件筛选出来清除再添加
    let arr = window.dePluginManager.Tools.map(ele => ele.name)
    list = list.filter(ele => arr.indexOf(ele) === -1)
    // 判断是显示的时候再添加
    if (tools.state !== 0) {
      list.push(tools.name)
    }
    store.commit('SET_OPENLIST', list)
    // 插件切换
    window.dePluginManager.changeTool(tools)
    return
  }
  // 判断是否是Widgets类型
  tools = window.dePluginManager.Widgets.find(
    ele => ele.name == plugin.plugName
  )
  if (tools) {
    if (tools.state !== 0) {
      // 如果需要显示则添加
      list.push(tools.name)

      // 开启挂件
      window.dePluginManager.showWidget(tools)
    } else {
      list = list.filter(ele => ele !== tools.name)
      window.dePluginManager.closeWidget(tools)
    }
    store.commit('SET_OPENLIST', list)

    return
  }
}
const closeDialog = () => {
  emit('closepluginDialogFlag', false)
}
const showcy = ref<boolean>(false)

// 常用列表的top值获取
const top = ref<number>(0)
let choosePlugin: any = null
// 右键事件 显示 保存插件信息
const addmenuflag = (event, plugin) => {
  let arr = store.state.app.toolsList.concat(store.state.app.widgetsList)
  let obj = arr.find(ele => ele.name == plugin.plugName)
  if (obj) {
    text.value = '从常用列表删除'
  } else {
    text.value = '添加到常用列表'
  }
  showcy.value = true
  top.value = event.clientY
  choosePlugin = plugin
}
const addToCY = () => {
  // 首先判断插件是哪个  tools或者widgets
  if (!choosePlugin) {
    return
  }
  let obj = window.dePluginManager.Tools.find(ele => {
    return ele.name == choosePlugin.plugName
  })
  if (obj) {
    let arr = store.state.app.toolsList || []
    let oldObj = arr.find(ele => ele.name == obj.name)
    if (oldObj) {
      arr = arr.filter(ele => ele.name !== obj.name)
    } else {
      arr.push(obj)
    }
    store.commit('SET_TOOLSLIST', arr)
    return
  }
  obj = window.dePluginManager.Widgets.find(ele => {
    return ele.name == choosePlugin.plugName
  })
  if (obj) {
    let arr = store.state.app.widgetsList || []
    let oldObj = arr.find(ele => ele.name == obj.name)
    if (oldObj) {
      arr = arr.filter(ele => ele.name !== obj.name)
    } else {
      arr.push(obj)
    }
    store.commit('SET_WIDGETSLIST', arr)
  }
}
// 获取左侧的图标
const getimg = data => {
  return `/assets/plugin/${data.annexName}/icon.png`
}
// 获取是否选中 选中颜色变化
const getborder = data => {
  return store.state.app.openList.indexOf(data.plugName) !== -1
}
</script>
<style lang="sass" scope>
.drag-dialog
  // margin: 100px 0 0 20px;
  .flex-svg-content
    display: flex
    overflow: hidden
    div
      line-height: 40px
      // margin-left: 20px;

    div:first-child img
      width: 16px
      height: 16px
      margin-top: 13px
      margin-left: 30px
      margin-right: 5px

    div:last-child
      flex: 1
      font-size: 15px

  .addcy
    line-height: 20px
    position: fixed
    width: 100px
    height: 20px
    text-align: center
    left: 130px
    font-size: 12px
    background: skyblue
    cursor: pointer

  .color
    background: #0b82a1 !important

  .borders
    transform: translateY(-60px)
    filter: drop-shadow(white 0 60px)
</style>
