export type EffectCheckState = 'checked' | 'unchecked' | 'indeterminate';

export interface SimulationConfigState {
  taskStatus: number;
  simTimestamp: number;
  redState: EffectCheckState;
  blueState: EffectCheckState;
  labelState: EffectCheckState;
  communicationState: EffectCheckState;
  routeState: EffectCheckState;
  probState: EffectCheckState;
  fireState: EffectCheckState;
  trailState: EffectCheckState;
  sensorState: EffectCheckState;
  entityConfigCommunication: boolean;
  entityConfigProb: boolean;
  entityConfigFire: boolean;
  entityConfigTrail: boolean;
  entityConfigLabel: boolean;
  entityConfigSensor: boolean;
}

export const state: SimulationConfigState = {
  taskStatus: 0,
  simTimestamp: 0,
  redState: 'checked',
  blueState: 'checked',
  labelState: 'checked',
  communicationState: 'checked',
  routeState: 'checked',
  probState: 'checked',
  fireState: 'checked',
  trailState: 'checked',
  sensorState: 'checked',
  entityConfigCommunication: true,
  entityConfigProb: true,
  entityConfigFire: true,
  entityConfigTrail: true,
  entityConfigLabel: true,
  entityConfigSensor: true,
}