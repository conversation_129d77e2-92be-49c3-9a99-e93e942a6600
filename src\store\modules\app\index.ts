import {
  Store as VuexStore,
  CommitOptions,
  DispatchOptions,
  Module
} from 'vuex'
import { RootState } from '../../../store/index'
import { state } from './state'
import { mutations, Mutations } from './mutations'
import type { AppState } from './state'

export { AppState }

export type AppStore<S=AppState> = Omit<VuexStore<S>,'getters'|'commit'>
&{
  commit<K extends keyof Mutations, P extends Parameters<Mutations[K]>[1]>(
    key: K,
    payload: P,
    options?: CommitOptions
  ): ReturnType<Mutations[K]>
}

export const store: Module<AppState, RootState> = {
  state,
  mutations
}