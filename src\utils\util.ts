export function formatThousand(num: number | string): string {
  return (num + '').replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,')
}

export function formatFix2(num: number | string): string {
  const str = num + ''
  const xsd = str.split('.')
  if (xsd.length == 1) {
    return str + '.00'
  }
  if (xsd.length > 1) {
    if (xsd[1].length < 2) {
      return str + '0'
    }
    return xsd[0] + '.' + xsd[1].slice(0, 2)
  }
  return ''
}

export function formatFix1(num: number | string): string {
  const str = num + ''
  const xsd = str.split('.')
  if (xsd.length == 1) {
    return str + '.0'
  }
  if (xsd.length > 1) {
    return xsd[0] + '.' + xsd[1].slice(0, 1)
  }
  return ''
}

export function formatYearMontDay(date: Date): string {
  const day = date.getDate()
  const month = date.getMonth() + 1
  const year = date.getFullYear()

  const mon = month > 9 ? month : `0${month}`
  const dayZero = date.getDate() > 9 ? day : `0${day}`
  return `${year}/${mon}/${dayZero}`
}

export function currentTime(date: Date): string {
  const hour = date.getHours()
  const min = date.getMinutes()
  const sec = date.getSeconds()
  const hourZero = hour > 9 ? hour : `0${hour}`
  const minZero = min > 9 ? min : `0${min}`
  const dayZero = sec > 9 ? sec : `0${sec}`
  return `${hourZero}:${minZero}:${dayZero}`
}
