<template>
  <div class="flex justify-end bg-side">
    <el-tooltip
      class="item"
      effect="dark"
      placement="top-start"
      v-for="(item, index) in arr"
      :content="item.name"
      :key="index"
    >
      <div
        class="h-6 m-2 flex justify-center items-center rightmenu"
        @click="widgetsClick(item, index)"
      >
        <img :src="geticon(item)" :class="{ borders: getborder(item) }" />
      </div>
      <div></div>
    </el-tooltip>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, onMounted } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { setting, plugin, searchbtn, user } from "../../assets/plugin/index";
const store = useStore();
const arr = computed(() => {
  return store.state.app.widgetsList;
});
// 右下角 挂件点击  因为挂件可以共存 所以分开调用close和show方法
const widgetsClick = (plugin: any, index: number) => {
  let widget = window.dePluginManager.Widgets.find(
    (ele) => ele.name == plugin.name
  );
  if (!widget) {
    return;
  }
  let list = store.state.app.openList;
  if (widget.state !== 0) {
    list.push(widget.name);
    window.dePluginManager.showWidget(widget);
  } else {
    list = list.filter((ele) => ele !== widget.name);
    window.dePluginManager.closeWidget(widget);
  }
  store.commit("SET_OPENLIST", list);
};
const geticon = (data) => {
  let obj = window.GVJ.allPlugin.Widgets.find(
    (ele) => ele.plugName == data.name
  );
  if (obj) {
    return `/assets/plugin/${obj.annexName}/icon.png`;
  } else {
    let icon = require("../../assets/plugin/hyperlink.png");
    return icon;
  }
};
// 获取是否选中
const getborder = (data) => {
  return store.state.app.openList.indexOf(data.name) !== -1;
};
</script>
<style scoped>
.bg-side {
  background: url("../../assets/images/footerside.png") repeat-x;
}
.rightmenu {
  width: 21px !important;
  outline: none !important;
  overflow: hidden;
  cursor: pointer;
}
img {
  width: 70% !important;
}
.borders {
  transform: translateY(-60px);
  filter: drop-shadow(white 0 60px);
}
</style>
