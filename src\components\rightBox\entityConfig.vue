<!--
 * @Author: 老范
 * @Date: 2025-05-12 14:27:16
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-23 17:13:19
 * @Description: 请填写简介
-->
<template>
  <div class="configBox">
    <div class="title">
      <span class="describe">实体配置</span>
      <span class="close" @click.stop="close">关闭</span>
    </div>
    <div class="body">
      <div class="table-title">
        <div>
          <span>通讯</span>
          <span>
            <el-switch
              v-model="communicationState"
              inline-prompt
              @change="changeCommunication"
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>探测</span>
          <span>
            <el-switch
              v-model="probState"
              @change="changeProb"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>交火</span>
          <span>
            <el-switch
              v-model="fireState"
              @change="changeFire"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>尾迹</span>
          <span>
            <el-switch
              v-model="trailState"
              @change="changeTrail"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>标牌</span>
          <span>
            <el-switch
              v-model="labelState"
              @change="changeLabel"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>雷达</span>
          <span>
            <el-switch
              v-model="sensorState"
              @change="changeSensor"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  ref,
  onMounted,
  onUnmounted,
  defineProps,
  watch,
  defineEmits,
} from 'vue'
interface InfoType {
  la: number // 纬度
  lo: number // 经度
  al: number // 高度
  speed: number // 速度
  na: string
}
const emit = defineEmits(['closeBox'])
const ifUpdate: any = ref(true)
let props = defineProps<{
  id: number
}>()
const clickEntityEffect = ref<any>()
const value1 = ref<any>()
const info = ref<InfoType | {}>({})

watch(
  () => props.id,
  newId => {
    if (newId) {
      clickEntityEffect.value = window.viewer.entities.getById(props.id)
      const effect = window.effectManager.effects.get(props.id + '_' + '0')
      console.log('🚀 ~ effect:', effect)
    }
  },
  {
    immediate: true,
  }
)
const communicationState: any = ref(true)
const probState: any = ref(true)
const fireState: any = ref(true)
const trailState: any = ref(true)
const labelState: any = ref(true)
const sensorState: any = ref(true)

// 改变通讯
const changeCommunication = (val: any) => {
  const effect = window.effectManager.effects.get(props.id + '_' + '0')
  effect.entity.show = val
}
// 改变探测
const changeProb = (val: any) => {
  const effect = window.effectManager.effects.get(props.id + '_' + '1')
  effect.entity.show = val
}
// 改变交火
const changeFire = (val: any) => {
  const effect = window.effectManager.effects.get(props.id + '_' + '2')
  effect.entity.show = val
}

// 改变尾迹
const changeTrail = (val: boolean) => {
  const trail = window.modelManager.trailPrimitive.trails.get(props.id)
  if(!trail) return;
  trail.polyLine.show = val
}
// 改变标牌
const changeLabel = (val: any) => {
  const effect = window.viewer.entities.getById(props.id)
  effect.label.show = val
  console.log('🚀 ~ changeLabel ~ effect:', effect)
}
// 改变雷达
const changeSensor = (val: any) => {
  const effect = window.effectManager.effects.get(props.id + '_' + '6')
  effect.graphic.visible = val
}

onMounted(() => {
  // init()
})
onUnmounted(() => {
  // init()
  ifUpdate.value = false
})
// 关闭弹层
const close = () => {
  ifUpdate.value = false
  emit('closeBox')
}
</script>
<style scoped>
.configBox {
  width: 11.875vw;
  height: 22.4259vh;
  /* background-color: rgba(16, 37, 63, 0.7); */
  background-color: #003f63;
  /*margin: 20px 6px;*/
  position: absolute;
  right: 20px;
  top: 70.4815vh;
  z-index: 2002;
  box-sizing: border-box;
  border-radius: 5px 5px 0 0;
  color: #fff;
  padding-bottom: 20px;
}
.configBox .title {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 10px 10px;
  border-bottom: 2px solid #00ffff;
  margin-bottom: 10px;
}

.configBox .title > span:nth-child(1) {
  padding-left: 10px;
  border-left: 4px solid #0ff;
}
.configBox .title .close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}

.configBox .title > p {
  text-align: center;
}
.entityInfoBox .body {
  height: 188px;
}
.configBox .body .table-title > div {
  height: 24px;
}
.configBox .body .table-title {
  display: flex;
  justify-content: space-between;
  color: aqua;
  /* background: #0c2e4c; */
  background-color: #003f63;
  padding: 0 15px;
  flex-direction: column;
  font-size: 12px;
  margin-bottom: 10px;
}

.configBox .body .table-title > div > span:nth-child(1) {
  border: 1px solid #00e0ff;
  border-radius: 3px;
  margin-right: 25px;
  padding: 0 8px;
}

.configBox .body .t-l {
  text-align: left !important;
  padding-left: 60px !important;
}
</style>
