
import { MutationTree } from 'vuex'
import { UserState } from './state'
import { UserMutationTypes } from './mutations-types'

export type Mutations<S = UserState> = {
  [UserMutationTypes.SET_TOKEN](state:S,token:string):void
  [UserMutationTypes.SET_NAME](state:S,name:string):void
  [UserMutationTypes.SET_ROLEID](state:S,roleId:string|number):void
  SET_ROLES(state:S,roles:any[]):void
}

// export const mutations:MutationTree<UserState>&Mutations = {
  
export const mutations: MutationTree<UserState> & Mutations = {
  SET_TOKEN(state: UserState, token: string) {
    state.token = token
  },
  SET_NAME(state: UserState, name: string) {
    state.name = name
  },
  SET_ROLEID(state: UserState, roleId: string|number) {
    state.roleId = roleId
  },
  SET_ROLES(state: UserState, roles: any[]) {
    state.roles = roles
  },

}