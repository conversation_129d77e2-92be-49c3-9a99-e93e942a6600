/*
 * @Author: 老范
 * @Date: 2025-04-10 14:10:40
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-27 15:16:34
 * @Description: 请填写简介
 */
// import * as Cesium from 'cesium'
import { getStore } from './store-utils'
export class TrailManager {
  constructor(viewer) {
    this.viewer = viewer
    this.trails = new Map()
    this.maxPoints = 200
    this.settings = {
      color: Cesium.Color.RED,
      width: 12.0,
    }
    this.polylineCollection = new Cesium.PolylineCollection()
    viewer.scene.primitives.add(this.polylineCollection)
    this._initWorker()
  }

  addTrail(trailId, initialData, options = {}) {
    this.worker.postMessage({
      type: 'CREATE_TRAIL',
      trailId,
      rawData: initialData,
      settings: this.settings,
    })
  }

  updateTrail(trailId, newData) {
    this.worker.postMessage({
      type: 'UPDATE_TRAIL',
      trailId,
      rawData: newData,
    })
  }
  _initWorker() {
    const workerUrl = new URL('./trailDataProcessor.js', import.meta.url)
    this.worker = new Worker(workerUrl, { type: 'module' })

    this.worker.onmessage = e => {
      const { type, trailId, positions, settings, error } = e.data

      if (error) {
        console.error(`Trail ${trailId} error:`, error)
        return
      }

      switch (type) {
        case 'TRAIL_UPDATE':
          this._updateGeometry(trailId, positions, this.settings)
          break
        case 'TRAIL_CREATED':
          this._createGeometry(trailId, positions, this.settings)
          break
      }

      //   document.getElementById('trailCount').textContent = this.trails.size;
    }
  }
  convertFloat64ArrayToCartesian3(float64Array) {
    // 验证数据长度是否为3的倍数
    if (float64Array.length % 3 !== 0) {
      throw new Error('Invalid Float64Array length. Must be a multiple of 3.')
    }

    const positions = []
    const length = float64Array.length

    // 每次读取三个元素生成一个Cartesian3
    for (let i = 0; i < length; i += 3) {
      const x = float64Array[i]
      const y = float64Array[i + 1]
      const z = float64Array[i + 2]
      positions.push(new Cesium.Cartesian3(x, y, z))
    }

    return positions
  }
  _createGeometry(trailId, positions, settings) {
    const cartesianPositions = this.convertFloat64ArrayToCartesian3(positions)
    const polyLine = this.polylineCollection.add({
      positions: cartesianPositions,
      with: 2.0,
      show: this._readTrailDisplayConfig(trailId),
      material: Cesium.Material.fromType('Color', {
        color: Cesium.Color.YELLOW,
      }),
    })
    this.trails.set(trailId, {
      polyLine,
    })
  }

  _updateGeometry(trailId, positions, settings) {
    const cartesianPositions = this.convertFloat64ArrayToCartesian3(positions)
    const trail = this.trails.get(trailId)
    if (!trail) return
    trail.polyLine.positions = cartesianPositions
  }
  removeTrail(trailId) {
    const trail = this.trails.get(trailId)
    if (trail) {
      this.polylineCollection.remove(trail.polyLine)
      this.trails.delete(trailId)
      this.worker.postMessage({
        type: 'REMOVE_TRAIL',
        trailId,
      })
    }
  }

  /** 读取尾迹显示/隐藏配置 */
  _readTrailDisplayConfig(na) {
    const store = getStore()
    const entity = window.viewer.entities.getById(na)
    const si = entity?.properties?.si?.getValue()
    if (!si) return false
    const { redState, blueState, trailState } = store.state.simulationConfig
    // 判断阵营是否勾选
    const sideChecked = si === 'red' ? redState === 'checked' : blueState === 'checked'
    if (!sideChecked) return false
    // 判断全局尾迹开关
    if (trailState === 'unchecked') return false
    return true
  }
}
