<template>
  <div class="w-full flex justify-between items-center" style="height: 30px">
    <left-footer-tools class="h-full w-80 flex-1"></left-footer-tools>
     <middle-footer-tools class="bg   flex-auto justify-center  h-full"></middle-footer-tools>
    <right-footer-tools class="h-full  flex-1"></right-footer-tools>
  </div>
</template>
<script setup>
import LeftFooterTools from './leftFooterTools.vue'
import RightFooterTools from './rightFooterTools.vue'
import MiddleFooterTools from './middleFooterTools.vue'
</script> 
<style scoped>
.bg {
  flex:2;
  background: url('../../assets/images/footer.png') no-repeat;
  background-size: 100% 100%;
  z-index: -1;
}
</style>