<template>
  <div class="flex flex-col justify-center bg-image size">
    <div class="flex justify-around px-2 mt-1">
      <div
        class="w-24 h-4 leading-4 text-black text-center mr-1 text-sm mt-1 hid"
        :style="{ backgroundColor: color }"
      >
        <el-tooltip
          class="item"
          effect="dark"
          placement="bottom"
          :content="props.timeTitle"
        >
          {{ props.timeTitle }}</el-tooltip
        >
      </div>
      <div class="font-digital">{{ date }}</div>
    </div>
    <div class="text-4xl text-center font-digital tracking-wider">
      {{ currentTimeStr }}
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, computed } from 'vue'
import { currentTime, formatYearMontDay } from '@/utils/util'

const props = defineProps({
  timeTitle: {
    type: String,
    required: true,
  },
  option: {
    required: true,
  },
  color: {
    type: String,
    default: 'yellow',
  },
})
const color = computed(() => props.color)
let getCurrentTime = null
const date = ref('')
const currentTimeStr = ref('')
let interval: any
onMounted(() => {
  let option: any = props.option
  let time = option.date || new Date()
  let num = 0
  if (option.type == 'local') {
    date.value = formatYearMontDay(time)
    currentTimeStr.value = currentTime(time)
    interval = setInterval(() => {
      let timer = new Date(time).getTime()
      date.value = formatYearMontDay(new Date(timer + 1000 * num))
      currentTimeStr.value = currentTime(new Date(timer + 1000 * num))
      num += 1
    }, 1000)
  } else if (option.type == 'update') {
    if (option.callback) {
      option.callback(updateTime)
    }
  } else if (option.type == 'cesium') {
    interval = setInterval(() => {
      let timer = new Date(window.viewer.clock.currentTime)
      date.value = formatYearMontDay(timer)
      currentTimeStr.value = currentTime(timer)
    }, 500)
  }
})
const updateTime = (dates: Date) => {
  date.value = formatYearMontDay(dates)
  currentTimeStr.value = currentTime(dates)
}
onUnmounted(() => {
  // getCurrentTime = null
  if (!!interval) {
    clearInterval(interval)
  }
})
</script>

<style lang="sass" scoped>
.size
  height: 55px
  width: 157px
  .font-digital
    font-family: "digital"
    color: rgb(116, 190, 219)

.hid
  overflow: hidden
  text-overflow: ellipsis
  white-space: normal

.bg-image
  background: url("../../assets/images/bgtime.png") no-repeat
  /* background-size: contain; */
</style>
