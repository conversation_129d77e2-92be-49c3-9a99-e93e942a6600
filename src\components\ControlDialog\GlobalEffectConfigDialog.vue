<template>
  <el-dialog
    v-model="store.state.app.globalEffectBoxShow"
    :close-on-click-modal="false"
    title="态势显示配置"
    width="20%"
    draggable
    top="20vh"
  >
    <div class="g-row">
      <el-row class="g-title">
        <span class="g-label">全局实体配置</span>
        <img class="g-icon" src="/image/chart_line_img.png" alt="" />
      </el-row>
      <div class="g-content">
        <el-row class="g-group">
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(redState)"
              alt=""
              width="15"
              @click="
                () => {
                  redState = toggleState(redState)
                  changeSide(redState, 'red')
                }
              "
            />
            红方
          </el-col>
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(blueState)"
              alt=""
              width="15"
              @click="
                () => {
                  blueState = toggleState(blueState)
                  changeSide(blueState, 'blue')
                }
              "
            />
            蓝方
          </el-col>
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(labelState)"
              alt=""
              width="15"
              @click="
                () => {
                  labelState = toggleState(labelState)
                  changeLabel(labelState)
                }
              "
            />
            标牌
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="g-row">
      <el-row class="g-title">
        <span class="g-label">全局特效配置</span>
        <img class="g-icon" src="/image/chart_line_img.png" alt="" />
      </el-row>
      <div class="g-content">
        <el-row class="g-group">
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(communicationState)"
              alt=""
              width="15"
              @click="
                () => {
                  communicationState = toggleState(communicationState)
                  changeCommunication(communicationState)
                }
              "
            />
            通讯
          </el-col>
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(routeState)"
              alt=""
              width="15"
              @click="
                () => {
                  routeState = toggleState(routeState)
                  changeRoute(routeState)
                }
              "
            />
            航路
          </el-col>
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(probState)"
              alt=""
              width="15"
              @click="
                () => {
                  probState = toggleState(probState)
                  changeProb(probState)
                }
              "
            />
            探测
          </el-col>
        </el-row>
        <el-row class="g-group">
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(fireState)"
              alt=""
              width="15"
              @click="
                () => {
                  fireState = toggleState(fireState)
                  changeFire(fireState)
                }
              "
            />
            交火
          </el-col>
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(trailState)"
              alt=""
              width="15"
              @click="
                () => {
                  trailState = toggleState(trailState)
                  changeTrail(trailState)
                }
              "
            />
            尾迹
          </el-col>
          <el-col :span="8" class="g-item">
            <img
              :src="getStateIcon(sensorState)"
              alt=""
              width="15"
              @click="
                () => {
                  sensorState = toggleState(sensorState)
                  changeSensor(sensorState)
                }
              "
            />
            雷达
          </el-col>
        </el-row>
        <div class="title">尾迹点数限制</div>
        <el-slider
          v-model="trailPointCount"
          :min="0"
          :max="5000"
          :format-tooltip="value => `${value}点`"
          @change="changeTrailPointCount"
          class="g-slider"
        />
        <div class="title">模型基础尺寸控制</div>
        <el-slider
          v-model="modelSize"
          :min="1"
          :max="5"
          :step="1"
          :format-tooltip="value => `${value}倍`"
          @change="changeModelSize"
          class="g-slider"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, inject } from 'vue'
import { useStore } from 'vuex'

const store = useStore()

const redState = computed({
  get: () => store.state.simulationConfig.redState,
  set: v => store.commit('SET_RED_STATE', v),
})
const blueState = computed({
  get: () => store.state.simulationConfig.blueState,
  set: v => store.commit('SET_BLUE_STATE', v),
})
const labelState = computed({
  get: () => store.state.simulationConfig.labelState,
  set: v => store.commit('SET_LABEL_STATE', v),
})
const communicationState = computed({
  get: () => store.state.simulationConfig.communicationState,
  set: v => store.commit('SET_COMMUNICATION_STATE', v),
})
const routeState = computed({
  get: () => store.state.simulationConfig.routeState,
  set: v => store.commit('SET_ROUTE_STATE', v),
})
const probState = computed({
  get: () => store.state.simulationConfig.probState,
  set: v => store.commit('SET_PROB_STATE', v),
})
const fireState = computed({
  get: () => store.state.simulationConfig.fireState,
  set: v => store.commit('SET_FIRE_STATE', v),
})
const trailState = computed({
  get: () => store.state.simulationConfig.trailState,
  set: v => store.commit('SET_TRAIL_STATE', v),
})
const sensorState = computed({
  get: () => store.state.simulationConfig.sensorState,
  set: v => store.commit('SET_SENSOR_STATE', v),
})

const currentEntityId = inject('currentEntityId', ref(''))
const currentSide = inject('currentSide', ref(''))

// 初始化尾迹点数为300（与trailDataProcessor.js中的默认值保持一致）
const trailPointCount = ref(300)
// 模型尺寸
const modelSize = ref(1)

// 改变模型尺寸
const changeModelSize = (val: number) => {
  // 如果modelManager存在，更新模型尺寸
  if ((window as any).modelManager) {
    const config = (window as any).modelManager.updateModelSize(val)
    console.log(
      `模型尺寸已调整为${val}级别: minimumPixelSize=${config.minimumPixelSize}, maximumScale=${config.maximumScale}`
    )
  }
}

// 改变模型尾迹
const changeTrailPointCount = (val: number) => {
  // 如果modelManager存在，更新尾迹点数
  if (
    (window as any).modelManager &&
    (window as any).modelManager.trailPrimitive
  ) {
    // 向Worker发送消息，更新maxPoints
    ;(window as any).modelManager.trailPrimitive.worker.postMessage({
      type: 'UPDATE_MAX_POINTS',
      maxPoints: val,
    })
  }
}

// 改变阵营
const changeSide = (val: string, type: string) => {
  const entities = (window as any).viewer.entities.values
  const show = val === 'checked'
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]
    if (
      entity.properties &&
      entity.properties.si &&
      entity.properties.si.getValue() === type
    ) {
      entity.model.show.setValue(show)
      if (entity.label) {
        if (typeof entity.label.show.setValue === 'function') {
          entity.label.show.setValue(show && labelState.value === 'checked')
        } else {
          entity.label.show = show && labelState.value === 'checked'
        }
        if (currentEntityId.value && type === currentSide.value)
          store.commit(
            'SET_ENTITY_CONFIG_LABEL',
            show && labelState.value === 'checked'
          )
      }
      const entityId = entity.id
      if (
        (window as any).modelManager &&
        (window as any).modelManager.trailPrimitive
      ) {
        const trail = (window as any).modelManager.trailPrimitive.trails.get(
          entityId
        )
        if (trail && trail.polyLine) {
          trail.polyLine.show = show && trailState.value === 'checked'
          if (currentEntityId.value && type === currentSide.value)
            store.commit(
              'SET_ENTITY_CONFIG_TRAIL',
              show && trailState.value === 'checked'
            )
        }
      }
      if (
        (window as any).effectManager &&
        (window as any).effectManager.effects
      ) {
        const effects = (window as any).effectManager.effects
        const prefix = entityId + '_'
        effects.forEach((effect: any, key: string) => {
          if (key.startsWith(prefix)) {
            if (key.endsWith('_0') && effect.entity) {
              effect.entity.polyline.show.setValue(
                show && communicationState.value === 'checked'
              )

              if (currentEntityId.value && type === currentSide.value)
                store.commit(
                  'SET_ENTITY_CONFIG_COMMUNICATION',
                  show && communicationState.value === 'checked'
                )
            }
            if (key.endsWith('_1') && effect.entity) {
              effect.entity.polyline.show.setValue(show && probState.value === 'checked')
              if (currentEntityId.value && type === currentSide.value)
                store.commit(
                  'SET_ENTITY_CONFIG_PROB',
                  show && probState.value === 'checked'
                )
            }
            if (key.endsWith('_2') && effect.entity) {
              effect.entity.polyline.show.setValue(show && fireState.value === 'checked')
              if (currentEntityId.value && type === currentSide.value)
                store.commit(
                  'SET_ENTITY_CONFIG_FIRE',
                  show && fireState.value === 'checked'
                )
            }
            if (key.endsWith('_6') && effect.graphic) {
              effect.graphic.visible = show && sensorState.value === 'checked'
              if (currentEntityId.value && type === currentSide.value)
                store.commit(
                  'SET_ENTITY_CONFIG_SENSOR',
                  show && sensorState.value === 'checked'
                )
            }
          }
        })
      }
    }
  }
}

// 改变通讯
const changeCommunication = (val: string) => {
  const effects = (window as any).effectManager.effects
  const show = val === 'checked'
  effects.forEach((effect: any, key: string) => {
    if (key.endsWith('_0')) {
      effect.entity.polyline.show.setValue(show)
    }
  })
  if (!currentEntityId.value) return
  store.commit('SET_ENTITY_CONFIG_COMMUNICATION', show)
}

// 改变航路
const changeRoute = (val: string) => {
  const entities = (window as any).viewer.entities.values.filter((i: any) =>
    i.id.includes('_initRoute')
  )
  const show = val === 'checked'
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]
    entity.show = show
  }
}

// 改变探测
const changeProb = (val: string) => {
  const effects = (window as any).effectManager.effects
  const show = val === 'checked'
  effects.forEach((effect: any, key: string) => {
    if (key.endsWith('_1')) {
      effect.entity.polyline.show.setValue(show)
    }
  })
  if (!currentEntityId.value) return
  store.commit('SET_ENTITY_CONFIG_PROB', show)
}

// 改变交火
const changeFire = (val: string) => {
  const effects = (window as any).effectManager.effects
  const show = val === 'checked'
  effects.forEach((effect: any, key: string) => {
    if (key.endsWith('_2')) {
      effect.entity.polyline.show.setValue(show)
    }
  })
  if (!currentEntityId.value) return
  store.commit('SET_ENTITY_CONFIG_FIRE', show)
}

// 改变尾迹
const changeTrail = (val: string) => {
  if (!(window as any)?.modelManager) return console.warn('查找模型尾迹失败')
  const trails = Array.from(
    (window as any).modelManager.trailPrimitive.trails.values()
  )
  const show = val === 'checked'
  for (let i = 0; i < trails.length; i++) {
    const trail = trails[i]
    if (trail?.polyLine) trail.polyLine.show = show
  }
  if (!currentEntityId.value) return
  store.commit('SET_ENTITY_CONFIG_TRAIL', show)
}

// 改变雷达
const changeSensor = (val: string) => {
  const effects = (window as any).effectManager.effects
  const show = val === 'checked'
  effects.forEach((effect: any, key: string) => {
    if (key.endsWith('_6')) {
      if (effect.graphic) {
        effect.graphic.visible = show
      }
    }
  })
  if (!currentEntityId.value) return
  store.commit('SET_ENTITY_CONFIG_SENSOR', show)
}

// 改变标牌
const changeLabel = (val: string) => {
  const entities = (window as any).viewer.entities.values
  const show = val === 'checked'
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]
    if (entity.label) {
      if (typeof entity.label.show.setValue === 'function') {
        entity.label.show.setValue(show)
      } else {
        entity.label.show = show
      }
    }
  }
  if (!currentEntityId.value) return
  store.commit('SET_ENTITY_CONFIG_LABEL', show)
}

// 三态图片路径
function getStateIcon(state: string) {
  if (state === 'checked') return '/image/selected_icon.png'
  if (state === 'indeterminate') return '/image/indeterminate_icon.png' // 半选图片占位
  return '/image/unselected_icon.png'
}
// 三态切换
function toggleState(state: string) {
  if (state === 'checked') return 'unchecked'
  return 'checked'
}

watch(
  () => store.state.simulationConfig.taskStatus,
  val => {
    store.commit('SET_GlobalEffectBoxShow', false)
    store.commit('SET_RED_STATE', 'checked')
    store.commit('SET_BLUE_STATE', 'checked')
    store.commit('SET_LABEL_STATE', 'checked')
    store.commit('SET_COMMUNICATION_STATE', 'checked')
    store.commit('SET_ROUTE_STATE', 'checked')
    store.commit('SET_PROB_STATE', 'checked')
    store.commit('SET_FIRE_STATE', 'checked')
    store.commit('SET_TRAIL_STATE', 'checked')
    store.commit('SET_SENSOR_STATE', 'checked')
  }
)

watch(
  () => store.state.app.globalEffectBoxShow,
  val => {
    if (val) {
      // 标牌
      const entities = (window as any).viewer?.entities?.values || []
      let hasLabelTrue = false,
        hasLabelFalse = false
      for (let i = 0; i < entities.length; i++) {
        const entity = entities[i]
        if (entity.label && entity.label.show !== undefined) {
          const show =
            typeof entity.label.show.getValue === 'function'
              ? entity.label.show.getValue()
              : entity.label.show
          if (show) hasLabelTrue = true
          else hasLabelFalse = true
        }
      }
      store.commit(
        'SET_LABEL_STATE',
        hasLabelTrue && hasLabelFalse
          ? 'indeterminate'
          : hasLabelTrue
          ? 'checked'
          : 'unchecked'
      )
      // 全局特效配置
      const effects = (window as any).effectManager?.effects
      const checkEffect = (suffix: string, mutation: string) => {
        let hasTrue = false,
          hasFalse = false
        effects?.forEach((effect: any, key: string) => {
          if (key.endsWith(suffix)) {
            let show = false
            if (suffix === '_6') show = effect.graphic?.visible
            else show = effect.entity?.show
            if (typeof show?.getValue === 'function') show = show.getValue()
            if (show) hasTrue = true
            else hasFalse = true
          }
        })
        store.commit(
          mutation,
          hasTrue && hasFalse
            ? 'indeterminate'
            : hasTrue
            ? 'checked'
            : 'unchecked'
        )
      }
      checkEffect('_0', 'SET_COMMUNICATION_STATE')
      checkEffect('_1', 'SET_PROB_STATE')
      checkEffect('_2', 'SET_FIRE_STATE')
      checkEffect('_6', 'SET_SENSOR_STATE')
      // 尾迹
      let hasTrailTrue = false,
        hasTrailFalse = false
      const trails = Array.from(
        (window as any).modelManager?.trailPrimitive?.trails?.values?.() || []
      )
      for (let i = 0; i < trails.length; i++) {
        const show =
          typeof trails[i]?.polyLine?.show?.getValue === 'function'
            ? trails[i].polyLine.show.getValue()
            : trails[i]?.polyLine?.show
        if (show) hasTrailTrue = true
        else hasTrailFalse = true
      }
      store.commit(
        'SET_TRAIL_STATE',
        hasTrailTrue && hasTrailFalse
          ? 'indeterminate'
          : hasTrailTrue
          ? 'checked'
          : 'unchecked'
      )
    }
  }
)
</script>

<style lang="less" scoped>
.g-row {
  .g-title {
    display: flex;
    align-items: center;

    .g-label {
      position: relative;
      color: #33acf4;

      margin-right: 8px;
      padding-left: 12px;
      letter-spacing: 1px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 7px;
        height: 7px;
        background-color: #33acf4;
        border-radius: 50%;
      }
    }

    .g-icon {
      flex: 1;
      flex-shrink: 0;
      height: 2px;
    }
  }
  .g-content {
    padding: 10px 10px 0;
    .g-group {
      margin-bottom: 10px;
      .g-item {
        display: flex;
        align-items: center;

        img {
          margin-right: 8px;
          cursor: pointer;
        }

        span {
          user-select: none;
        }
      }
    }
  }
}

.g-slider {
  transform: scale(0.8);
}
</style>
