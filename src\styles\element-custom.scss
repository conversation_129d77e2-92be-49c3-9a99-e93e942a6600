*{
  .el-drawer {
    background-color: rgba(49, 61, 80, 0.7) !important;
    border-radius: 5px !important;
    padding: 0 20px 20px 20px !important;
  }
  .el-drawer__header {
    margin-bottom: 0px !important;
  }
  .el-drawer__close {
    color: whitesmoke !important;
    padding: 2px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
  }
  
  .el-table {
    background: transparent !important;
  }
  .el-table tr,
  .el-table th {
    background: transparent !important;
    color: whitesmoke !important;
  }
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: rgba(49, 61, 80, 0.7)  !important;
  }
  .el-collapse {
    border: none  !important;
  }
  .el-collapse-item__header {
    padding-left: 10px !important;
    background-color: transparent !important;
    color: #fff !important;
    border: 1px solid rgb(49, 61, 80) !important;
  }
  .el-collapse-item__wrap {
    font-size: 14px !important;
    background-color: transparent !important;
    border: 1px solid rgb(49, 61, 80) !important;
  }
  
  .el-collapse-item__content > div:nth-child(odd) {
    background-color: rgba(6, 43, 57, 0.78) !important;
  }
  .el-collapse-item__content > div:nth-child(even) {
    background-color: rgba(0, 30, 40, 0.9) !important;
  }
  .el-collapse-item__content > div:hover {
    background-color: rgba(100, 187, 240, 0.78) !important;
  }
  .el-collapse-item{
    
  }
  .el-collapse-item__content {
    color: whitesmoke !important;
    padding-bottom: 6px !important;
    cursor: pointer !important;
  }
  
  .el-message-box {
    @apply border-0 !important;
    background-color: rgba(49, 61, 80, 0.7) !important;
  }
  .el-message-box__content {
    color: whitesmoke !important;
  }
  .el-message-box__btns .el-button {
    color: whitesmoke !important;
    background: transparent !important;
  }
  .el-tree {
    background-color: transparent !important;
    color: whitesmoke !important;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: rgba(108, 153, 224, 0.7) !important;
  }
  .el-tree-node__content:hover {
    background-color: rgba(108, 153, 224, 0.7) !important;
  }
  .plugin .el-dialog {
    margin-top: 90px !important;
    margin: 5vh !important;
    background-color: rgba(108, 153, 224, 0.7) !important;
  }
  
  .plugin .el-dialog .el-dialog__header .el-dialog__title {
    font-size: 15px !important;
    color: #fff !important;
  }
  
  .el-popover.el-popper {
    background-color: rgba(0, 30, 40, 0.78) !important;
    border: none !important; 
  }
  .el-popconfirm__main {
    color: #fff !important;
  }
  .el-tree-node__content>label.el-checkbox{
    margin-top: 18px !important;
  }
  .search .el-input .el-input__inner  .el-input-group__append{
    
    
      color: #fff !important;
      background-color: rgba(0, 30, 40, 0.78)  !important;
      border-color: rgba(0, 30, 40, 0.78)  !important;
    
  }
  
}