import {
  Store as VuexStore,
  CommitOptions,
  DispatchOptions,
  Module
} from 'vuex'
import { RootState } from '../../../store/index'
import { state } from './state'
import { mutations, Mutations } from './mutations'
import type { UserState } from './state'

export { UserState }

export type UserStore<S=UserState> = Omit<VuexStore<S>,'getters'|'commit'|'dispatch'>
&{
  commit<K extends keyof Mutations, P extends Parameters<Mutations[K]>[1]>(
    key: K,
    payload: P,
    options?: CommitOptions
  ): ReturnType<Mutations[K]>
}

export const store: Module<UserState, RootState> = {
  state,
  mutations
}