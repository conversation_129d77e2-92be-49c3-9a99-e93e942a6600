<!--
 * @Author: 老范
 * @Date: 2025-03-20 17:50:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-20 14:08:31
 * @Description: 请填写简介
-->
<template>
  <div class="flex justify-end bg">
    <div
      class="
        border-2
        w-10
        h-10
        flex
        justify-center
        items-center
        mr-2
        mt-2
        cursor-pointer
        item
      "
      v-for="item in arr"
      :key="item"
      @click="rightClick(item.name)"
    >
      <el-tooltip class="item" effect="dark" :content="item.name">
        <div><img :src="item.img" style="width: 20px" /></div>
      </el-tooltip>
    </div>
    <div class="h-11 text-center pt-4 pr-2 cursor-pointer">{{ username }}</div>
    <PluginDialog
      :pluginDialogFlag="pluginDialogFlag"
      :title="title"
      @closepluginDialogFlag="closepluginDialogFlag"
    />
    <login v-if="loginFlag" @closeLogin="closeLogin"></login>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue'
import { setting, plugin, searchbtn, user } from '../../assets/plugin/index'
import PluginDialog from '../pluginDialog/index.vue'
import login from '@/views/login/index.vue'
import { useStore } from 'vuex'

const title = '插件列表'
const arr = [
  { name: '设置', img: '/image/setting.png' },
  { name: '插件', img: '/image/plugin.png' },
  { name: '搜索', img: '/image/search.png' },
  { name: '用户', img: '/image/user.png' },
]
const store = useStore()
console.log(store)
const username = computed(() => {
  return store.state.user.name
})
const pluginDialogFlag = ref(false)
let searchFlag = ref(false)
const loginFlag = ref(false)
let userInfoFlag = ref(false)
const rightClick = (name: string): void => {
  switch (name) {
    case '插件':
      pluginDialogFlag.value = !pluginDialogFlag.value
      break
    case '用户':
      loginFlag.value = !loginFlag.value
  }
}
const closepluginDialogFlag = (flag: boolean) => {
  pluginDialogFlag.value = flag
}
const closeLogin = (flag: boolean) => {
  loginFlag.value = flag
}
</script>
<style lang="sass" scoped>
.bg
  height: 92px
  width: 998px
  .item
    border-color: #144759c7

  .item:hover
    border-color: #74bedb
</style>

<!-- <style lang="sass" scoped>
.bg
  height: 92px
  width: 998px
  background: url('../../assets/images/backside.png') repeat-x
  background-position-y: -10px
  .item
    border-color: #144759c7

  .item:hover
    border-color: #74bedb
</style> -->
