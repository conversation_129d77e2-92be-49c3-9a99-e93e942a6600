@import './element-custom.css';
@import './plugin.css';
@font-face {
  font-family: 'digital';
  src: url('../assets/fonts/digital.ttf');
}

body,
html {
  margin: 0;

  padding: 0;
  font-size: 14px;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, SimSun, sans-serif, digital;
  font-weight: 400;
  overflow: hidden;
  color: whitesmoke;
  /* -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
  background-color: rgb(var(--bg));
  color: rgb(var(--text-color));
  user-select: none;
  overflow: hidden; */
}
* {
  box-sizing: border-box;
  outline: none;
}
/* #distanceLegendDiv .cesium-svgPath-svg{
  width: 0 !important;
  height: 0 !important;
} */
/* .compass{
  z-index:99
}
.navigation-controls{
  right:33px;
  bottom:90px
} */


/* ::-webkit-scrollbar {
  width: 4px;
  height: 6px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: none;
} */

/* :root {
  --bg: 0, 0, 0;
  --text-color: 255, 255, 255;
  --primary: 0, 46, 83;
  --active: 0, 155, 255;
} */

#app {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.timeLine-strag-div .timeline-main-div{
  border-width:0 !important
}

::-webkit-scrollbar {
  width: 5px;
  background-color: rgba(119, 135, 141, 0.78);
}
::-webkit-scrollbar-thumb {
  background-color: rgba(161, 194, 210, 0.78);
}
::-webkit-scrollbar-track {
  background-color: rgba(119, 135, 141, 0.78);
}
iframe{
  z-index: 0 !important
}
