<template>
  <div
    class="
      bg-side
      p-2
      flex
      items-center
      justify-between
      text-blue-300
      opacity-80
    "
  >
    <div class="flex justify-between flex-1">
      <div>经度 {{ longitude }}</div>
      <div>纬度 {{ latitude }}</div>
      <div>层级 {{ level }}</div>
      <div style="width: 110px">
        <div class="map-scale-bar" :style="{ width: mapscale.barWidth + 'px' }">
          <span>
            {{ mapscale.distanceLabel }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
const longitude = ref<number>(0)
const latitude = ref<number>(0)
const level = ref<number>(0)
// 开启鼠标监听事件
const handler = new Cesium.ScreenSpaceEventHandler(window.viewer.scene.canvas)
handler.setInputAction(event => {
  const position: any = event
  // 根据屏幕坐标获取坐标位置
  const point: any = window.GV.GeoPoint.fromScreen(
    position.endPosition.x,
    position.endPosition.y,
    window.viewer
  )
  if (!point) {
    return
  }
  // 渲染数据
  longitude.value = point.lon.toFixed(5) * 1
  latitude.value = point.lat.toFixed(5) * 1
  level.value = window.viewer.iCamera.currentLevel
}, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
// 视角变化时修改图层
window.viewer.iCamera.changed.addEventListener(function () {
  level.value = window.viewer.iCamera.currentLevel
})
const mapscale = ref({
  _removeSubscription: undefined,
  _lastLegendUpdate: undefined,
  distanceLabel: undefined,
  barWidth: undefined,
})
// 获取比例尺
window.viewer.scene.postRender.addEventListener(() => {
  updateDistanceLegendCesium(mapscale.value, window.viewer.scene)
})
let distances = [
  1, 2, 3, 5, 10, 20, 30, 50, 100, 200, 300, 500, 1e3, 2e3, 3e3, 5e3, 1e4, 2e4,
  3e4, 5e4, 1e5, 2e5, 3e5, 5e5, 1e6, 2e6, 3e6, 5e6, 1e7, 2e7, 3e7, 5e7,
]
let geodesic = new Cesium.EllipsoidGeodesic()
const updateDistanceLegendCesium = (viewModel: any, scene: Cesium.Scene) => {
  let now = (Cesium as any).getTimestamp()
  if (now < viewModel._lastLegendUpdate + 250) {
    return
  }
  if (!scene || !scene.canvas) {
    return
  }
  viewModel._lastLegendUpdate = now
  let width = scene.canvas.clientWidth
  let height = scene.canvas.clientHeight
  let left = scene.camera.getPickRay(
    new Cesium.Cartesian2((width / 2) | 0, height - 1)
  )
  let right = scene.camera.getPickRay(
    new Cesium.Cartesian2((1 + width / 2) | 0, height - 1)
  )
  let globe = scene.globe
  let leftPosition = globe.pick(left, scene)
  let rightPosition = globe.pick(right, scene)
  if (!Cesium.defined(leftPosition) || !Cesium.defined(rightPosition)) {
    viewModel.barWidth = undefined
    viewModel.distanceLabel = undefined
    return
  }
  let leftCartographic = globe.ellipsoid.cartesianToCartographic(leftPosition)
  let rightCartographic = globe.ellipsoid.cartesianToCartographic(rightPosition)
  geodesic.setEndPoints(leftCartographic, rightCartographic)
  let pixelDistance = geodesic.surfaceDistance
  let maxBarWidth = 100
  let distance
  for (let i = distances.length - 1; !Cesium.defined(distance) && i >= 0; --i) {
    if (distances[i] / pixelDistance < maxBarWidth) {
      distance = distances[i]
    }
  }
  if (Cesium.defined(distance)) {
    let label
    if (distance >= 1e3) {
      label = (distance / 1e3).toString() + ' km'
    } else {
      label = distance.toString() + ' m'
    }
    viewModel.barWidth = (distance / pixelDistance) | 0
    viewModel.distanceLabel = label
  } else {
    viewModel.barWidth = undefined
    viewModel.distanceLabel = undefined
  }
}
</script>
<style scoped lang='sass'>
.bg-side
  background: url("../../assets/images/footerside.png") repeat-x

.map-scale-bar
  border-left: 1px solid #fff
  border-right: 1px solid #fff
  border-bottom: 1px solid #fff
  height: 10px
  margin-left: 20px
  position: relative
  text-align: center
  margin-top: 10px

  span
    position: absolute
    top: -12px
    width: 100px
    margin-left: -50px
</style>
