import { MutationTree } from 'vuex'
import { SimulationConfigState } from './state'
import { SimulationConfigMutationTypes } from './mutations-types'

export type Mutations<S = SimulationConfigState> = {
  [SimulationConfigMutationTypes.SET_TASK_STATUS](state: S, status: number): void
  [SimulationConfigMutationTypes.SET_SIM_TIMESTAMP](state: S, ts: number): void
}

export const mutations: MutationTree<SimulationConfigState> & Mutations = {
  [SimulationConfigMutationTypes.SET_TASK_STATUS](state: SimulationConfigState, status: number) {
    state.taskStatus = status
  },
  [SimulationConfigMutationTypes.SET_SIM_TIMESTAMP](state: SimulationConfigState, ts: number) {
    state.simTimestamp = ts
  },
  [SimulationConfigMutationTypes.SET_RED_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.redState = val
  },
  [SimulationConfigMutationTypes.SET_BLUE_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.blueState = val
  },
  [SimulationConfigMutationTypes.SET_LABEL_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.labelState = val
  },
  [SimulationConfigMutationTypes.SET_COMMUNICATION_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.communicationState = val
  },
  [SimulationConfigMutationTypes.SET_ROUTE_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.routeState = val
  },
  [SimulationConfigMutationTypes.SET_PROB_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.probState = val
  },
  [SimulationConfigMutationTypes.SET_FIRE_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.fireState = val
  },
  [SimulationConfigMutationTypes.SET_TRAIL_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.trailState = val
  },
  [SimulationConfigMutationTypes.SET_SENSOR_STATE](state: SimulationConfigState, val: 'checked' | 'unchecked' | 'indeterminate') {
    state.sensorState = val
  },
  [SimulationConfigMutationTypes.SET_ENTITY_CONFIG_COMMUNICATION](state: SimulationConfigState, val: boolean) {
    state.entityConfigCommunication = val
  },
  [SimulationConfigMutationTypes.SET_ENTITY_CONFIG_PROB](state: SimulationConfigState, val: boolean) {
    state.entityConfigProb = val
  },
  [SimulationConfigMutationTypes.SET_ENTITY_CONFIG_FIRE](state: SimulationConfigState, val: boolean) {
    state.entityConfigFire = val
  },
  [SimulationConfigMutationTypes.SET_ENTITY_CONFIG_TRAIL](state: SimulationConfigState, val: boolean) {
    state.entityConfigTrail = val
  },
  [SimulationConfigMutationTypes.SET_ENTITY_CONFIG_LABEL](state: SimulationConfigState, val: boolean) {
    state.entityConfigLabel = val
  },
  [SimulationConfigMutationTypes.SET_ENTITY_CONFIG_SENSOR](state: SimulationConfigState, val: boolean) {
    state.entityConfigSensor = val
  },
}