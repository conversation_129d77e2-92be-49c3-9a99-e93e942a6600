import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import layout from '@/layout/index.vue'
import { useStore } from '@/store'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: import('../views/login/login.vue')
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  },
  {
    path: '/',
    component: layout,
    redirect: '/home',
    children: [
      {
        path: '/home',
        component: () => import(/* webpackChunkName: "dashboard" */ '@/views/home/<USER>'),
        name: 'home',
      }
    ]
  }
]
const asyncRouter: Array<RouteRecordRaw> = [
  {
    path: '/',
    component: layout,
    children: [
      {
        path: '/table1',
        component: () => import(/* webpackChunkName: "dashboard" */ '@/views/table1/table1.vue'),
        name: 'table1',
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

router.beforeEach(async (to: any, from: any, next: any) => {
  const store = useStore()
  // 判断直接过去的路由
  if (to.path == '/login') {
    next()
  } else if (!sessionStorage.getItem('userId')) {
    next({
      path: '/login'
    })
  } else {
    if (store.state.user.roles.length === 0) {
      try{
        store.commit('SET_ROLES', asyncRouter)
        asyncRouter.map(ele => {
          router.addRoute(ele)
        })
        next({ ...to, replace: true })
      } catch{
        console.log('错误')
      }
    }else{
      next()
    }
  }

})




export default router
