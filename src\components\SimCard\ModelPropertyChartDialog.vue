<template>
  <el-dialog
    v-model="visible"
    :title="`${props.id} - ${title}`"
    width="40%"
    @close="handleClose"
    draggable
    append-to-body
  >
    <div ref="chartRef" style="width: 100%; height: 500px"></div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, onBeforeUnmount, watch, computed } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { useStore } from 'vuex'

const props =
  defineProps<{ id: string | number; type: string; visible: boolean }>()
const emit = defineEmits(['update:visible'])
const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null
let timer: number | null = null
const data: { time: string[]; value: number[] } = { time: [], value: [] }
const store = useStore()

const propertyMap: Record<string, { label: string; unit: string }> = {
  lo: { label: '经度', unit: '(度)' },
  la: { label: '纬度', unit: '(度)' },
  al: { label: '高度', unit: '(米)' },
  sp: { label: '速度', unit: '(米/秒)' },
  pi: { label: '俯仰', unit: '(度)' },
  ya: { label: '偏航', unit: '(度)' },
  ro: { label: '翻滚', unit: '(度)' },
}

const title = computed(() => `${propertyMap[props.type].label || props.type} - 时间变化曲线图`)
const yAxisName = computed(() => {
  const p = propertyMap[props.type]
  return p ? `${p.label}${p.unit}` : props.type
})

function handleClose() {
  emit('update:visible', false)
  stopTimer()
  // 关闭时清空数据
  data.time.length = 0
  data.value.length = 0
  if (chart) {
    chart.setOption({
      xAxis: { data: [] },
      series: [{ data: [] }]
    })
  }
}

function stopTimer() {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

function getModelPropertyValue(model: any, type: string): number | undefined {
  if (!model || !model.properties) return undefined
  const prop = model.properties[type]
  if (prop && typeof prop.getValue === 'function') {
    return prop.getValue()
  }
  return undefined
}

function startTimer() {
  stopTimer()
  timer = setInterval(() => {
    let model
    if (window.viewer) {
      model = window.viewer.entities.getById(props.id)
    }
    if (!model) {
      stopTimer()
      ElMessage.error('未找到模型')
      return
    }
    const value = getModelPropertyValue(model, props.type)
    if (value === undefined) {
      ElMessage.error('未找到模型或属性')
      stopTimer()
      return
    }
    // 在采样点处，x轴时间用store.state.simulationConfig.simTimestamp
    const nowTimestamp = store.state.simulationConfig.simTimestamp
    addDataPoint(String(nowTimestamp.toFixed(2)), value)
    updateChart()
  }, 2000)
}

function getYAxisRange() {
  if (data.value.length === 0) return {}
  const first = data.value[0]
  // 以第一帧为中心，跨度为1
  let yMin = Number((first - 0.5).toFixed(2))
  let yMax = Number((first + 0.5).toFixed(2))
  // 检查后续数据是否超出范围，动态扩展
  for (const v of data.value) {
    if (v < yMin) yMin = Number((v - 0.1).toFixed(2))
    if (v > yMax) yMax = Number((v + 0.1).toFixed(2))
  }
  // 计算跨度，向上取整到8的倍数
  let span = yMax - yMin
  let interval = span / 8
  const pow = Math.pow(10, Math.floor(Math.log10(interval)))
  interval = Math.ceil(interval / pow * 10) / 10 * pow
  span = interval * 8
  const center = (yMax + yMin) / 2
  yMin = Number((center - span / 2).toFixed(2))
  yMax = Number((center + span / 2).toFixed(2))
  return {
    min: yMin,
    max: yMax,
    interval,
    axisLabel: {
      color: '#fff',
      formatter: (v: number) => v.toFixed(2)
    },
    axisLine: { lineStyle: { color: 'rgba(180,180,180,0.3)' } },
    axisTick: { show: false },
    splitLine: {
      lineStyle: {
        color: 'rgba(180,180,180,0.2)'
      }
    }
  }
}

function updateChart() {
  if (chart) {
    const yRange = getYAxisRange()
    chart.setOption({
      tooltip: {
        textStyle: { color: '#000' }
      },
      xAxis: {
        data: data.time,
        axisLabel: { color: '#fff', fontSize: 15, margin: 30 },
        axisLine: { lineStyle: { color: 'rgba(180,180,180,0.3)' } },
        splitLine: { lineStyle: { color: 'rgba(180,180,180,0.2)' } },
        axisTick: { show: false },
        // name: '仿真时间(秒)', // 移除
        // nameLocation: 'end',
        // nameTextStyle: { ... },
        // nameGap: 70,
        // position: 'bottom',
      },
      yAxis: {
        ...yRange,
        name: yAxisName.value,
        nameTextStyle: { color: '#fff', fontSize: 18, fontFamily: 'Microsoft YaHei', padding: [0, 0, 10, 0] },
        nameGap: 30,
        axisLabel: { color: '#fff', fontSize: 15, margin: 18, formatter: (v: number) => v.toFixed(2) },
      },
      series: [{ data: data.value }],
      graphic: [{
        type: 'text',
        right: 20,
        bottom: 45,
        style: {
          text: '仿真时间(秒)',
          fill: '#fff',
          font: '18px \\"Microsoft YaHei\\", sans-serif',
        },
        z: 100
      }]
    })
  }
}

function addDataPoint(time: string, value: number) {
  data.time.push(time)
  data.value.push(value)
  // x轴最多100个点
  while (data.time.length > 100) data.time.shift()
  while (data.value.length > 100) data.value.shift()
}

onBeforeUnmount(() => {
  stopTimer()
  if (chart) {
    chart.dispose()
    chart = null
  }
})

watch(
  () => props.visible,
  async val => {
    if (val) {
      // 打开时清空数据
      data.time.length = 0
      data.value.length = 0
      await nextTick()
      if (!chart && chartRef.value) {
        chart = echarts.init(chartRef.value)
      }
      // 采集第一帧
      let model
      if (window.viewer) {
        model = window.viewer.entities.getById(props.id)
      }
      const value = getModelPropertyValue(model, props.type)
      const nowTimestamp = store.state.simulationConfig.simTimestamp
      if (value !== undefined) {
        addDataPoint(String(Math.round(nowTimestamp)), value)
      }
      if (chart) {
        const yRange = getYAxisRange()
        chart.setOption({
          tooltip: { textStyle: { color: '#000' } },
          xAxis: {
            type: 'category',
            data: data.time,
            axisLabel: { color: '#fff', fontSize: 15, margin: 30 },
            axisLine: { lineStyle: { color: 'rgba(180,180,180,0.3)' } },
            splitLine: { lineStyle: { color: 'rgba(180,180,180,0.2)' } },
            axisTick: { show: false },
            // name: '仿真时间(秒)', // 移除
            // nameLocation: 'end',
            // nameTextStyle: { ... },
            // nameGap: 70,
            // position: 'bottom',
          },
          yAxis: {
            ...yRange,
            name: yAxisName.value,
            nameTextStyle: { color: '#fff', fontSize: 18, fontFamily: 'Microsoft YaHei', padding: [0, 0, 10, 0] },
            nameGap: 30,
            axisLabel: { color: '#fff', fontSize: 15, margin: 18, formatter: (v: number) => v.toFixed(2) },
          },
          series: [{ type: 'line', data: data.value }],
          graphic: [{
            type: 'text',
            right: 20,
            bottom: 45,
            style: {
              text: '仿真时间(秒)',
              fill: '#fff',
              font: '18px \\"Microsoft YaHei\\", sans-serif',
            },
            z: 100
          }]
        })
      }
      startTimer()
    } else {
      stopTimer()
      // 关闭时清空数据（双保险）
      data.time.length = 0
      data.value.length = 0
      if (chart) {
        chart.setOption({
          xAxis: { data: [] },
          series: [{ data: [] }],
        })
      }
    }
  }
)
watch(
  () => props.type,
  () => {
    data.time.length = 0
    data.value.length = 0
    if (chart) {
      chart.setOption({
        xAxis: { data: [] },
        series: [{ data: [] }]
      })
    }
  }
)
</script>

<style scoped></style>
