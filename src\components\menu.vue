<template>
  <div class="All_content">
    <headers class="absolute z-50"></headers>
    <footers class="absolute bottom-0 z-50"></footers>
  </div>
</template>

<script lang="ts" setup>
import { Options, Vue } from 'vue-class-component'
import { defineProps, ref, defineEmits, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import headers from './header/index.vue'
import footers from './footer/index.vue'
</script>

<style scoped>
.All_content {
  width: 100%;
  height: 40px;
}
</style>
