<template>
  <div class="hello">

  </div>
</template>

<script lang="ts" setup>
import { Options, Vue } from "vue-class-component";
import { defineProps, ref, defineEmits } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from '@/store/index'
const router = useRouter();
const route = useRoute();
const emit = defineEmits(["change", "delete"]);
const props = defineProps({
  msg: String,
});
  // router.push({
  //   path:'/'
  // });
// const drawerRtlDisplay = computed(() => {
  // return store.state.drawerRtlDisplay
  // // return true
// })
// const name1 = ref<string>('tess')
  // useStore().commit('SET_NAME','这是一个名字')
</script>

<style scoped lang="sass">

</style>
