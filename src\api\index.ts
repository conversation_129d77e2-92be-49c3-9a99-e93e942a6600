import axios from 'axios'
import Vue from 'vue'
const $axios = axios.create({
  // 设置超时时间
  baseURL:(window as any).GVJ.URLS.pluginServer2
  // withCredentials: true,
  // 基础url，会在请求url中自动添加前置链接
  // baseURL: process.env.VUE_APP_BASE_API
})
// 请求拦截器
$axios.interceptors.request.use(
  async (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)
// 响应拦截器
$axios.interceptors.response.use(
  (response) => {
    const code = response.status
    if ((code >= 200 && code < 300) || code === 304) {
      return Promise.resolve(response.data)
    } else {
      return Promise.reject(response)
    }
  },
  (error) => {

    if (error.response) {
      switch (error.response.code) {
        case 401:
          break
        case 404:
          break
        default:

      }
    } else {
      // 请求超时或者网络有问题
      if (error.message.includes('timeout')) { } else { }
    }
    return Promise.reject(error)
  }
)
export default {
  post(url:string, data={}) {
    return $axios({
      url,
      method: 'post',
      data
    })
  },
  postByParams(url:string, params={}) {
    return $axios({
      url,
      method: 'post',
      params
    })
  },
  get(url:string, params={}) {
    return $axios({
      method: 'get',
      url,
      params
    })
  },
  delete(url:string, params={}) {
    return $axios({
      method: 'delete',
      url,
      params
    })
  }
}