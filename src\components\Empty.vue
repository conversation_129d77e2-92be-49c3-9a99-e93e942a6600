<script setup lang="ts">
const props = defineProps({
  width: {
    type: String,
    default: '100',
  },
  fontSize: {
    type: String,
    default: '14px',
  },
})
</script>
<template>
  <img
    class="empty-img"
    src="/image/empty_img.png"
    alt=""
    :width="props.width"
  />
  <p class="empty-text" :class="[`text-${props.fontSize}`]">暂无更多信息</p>
</template>
<style lang="less" scoped>
.empty-img {
  margin-bottom: 5px;
}
.empty-text {
  color: #909399;
}
</style>
