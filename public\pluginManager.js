
window.GVJ = {}
// 初始化插件列表
window.GVJ.createdePluginManager = function (viewer,list) {
console.log('GVBVGKJ',GV)
  // 初始化插件管理
  window.dePluginManager = new GV.DePluginManager(viewer)
  console.log('window.dePluginManager ', window.dePluginManager)
  window.dePluginViewManager = new GV.DePluginViewManager(viewer, document.body, function (div) {
    console.log("onCanvasChange", div)
  })
  // 根据用户信息获取对应的插件列表
  // let res = await fetch(localStorage.getItem('user'))

  // res//列表
  // 插件注册
  let res = {
    // 列表菜单
    tools: [
      {
        plugName: '标绘',
        annexName: 'vue-plottingPlugin',
      
      }
      
    ],
    // 工具  右下角
    Widgets: [
     
     
      // {
      //   plugName: '图层插件',
      //   annexName: 'ng-layerPlugin',
      //   showMenu: true
      // },
      {
        plugName: '量测',
        annexName: 'vue-measurePlugin',
        showMenu: true
      },
      {
        plugName: '地名',
        annexName: 'ng-placePlugin',
        showMenu: true
      },
      {
        plugName: '2D',
        annexName: 'morphPlugin',
        showMenu: true
      },
      {
        plugName: '卷帘',
        annexName: 'ng-rollerBlindPlugin',
        showMenu: true
      },
      {
        plugName: '罗盘',
        annexName: 'navigate-plugin',
        showMenu: true
      },
      {
        plugName: '鸟瞰图',
        annexName: 'eagleEyeLayer',
        showMenu: true
      },
      {
        plugName: '时间线',
        annexName: 'vue-timelinePlugin',
        showMenu: true
      },
    ]
  }
  if (list) {
    list.map(ele => {
      if (ele.annexPath) {
        let arr1 = ele.annexPath.split('/')
        let name = arr1[arr1.length - 1].split('.')[0]
        if(ele.type=='0'){
          res.tools.push({
            plugName: ele.plugName,
            annexName: name,
            showMenu: true
          })
        }else{
          res.Widgets.push({
            plugName: ele.plugName,
            annexName: name,
            showMenu: true
          })
        }
       
      }
    })
  }

  window.GVJ.allPlugin = res
  let index = 0
  for (let i in res) {
    res[i].map(ele => {
      let pluginScript = document.createElement('script')
      pluginScript.src = `/assets/plugin/${ele.annexName}/lead.js`
      let script = document.getElementsByTagName('head')[0].appendChild(pluginScript)
      // 状态  隐藏
      // 根据用户获取常用插件

      // let max = res.Widgets.length + res.tools.length

      // script.onload = () => {
      //   index += 1
      //   console.log(index,max)
      //   if (index == max) {
      //     for (let item of dePluginManager.Tools) {
      //       console.log('dePluginManagerdePluginManager', item)
      //       if(item.name == 'demo'){
      //         dePluginManager.showTool(item);
      //       }
      //     }
      //   }
      // }
    })
  }






  // 返回插件的树状结构 及常驻插件 （常驻插件":下方右侧）
  return res
}