/*
 * @Author: 老范
 * @Date: 2025-03-20 17:50:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-03-31 10:46:54
 * @Description: 请填写简介
 */
import { createApp } from 'vue'
import App from './App.vue'
// import router from './router'
import { store } from './store/index'
import { setStore } from './utils/store-utils'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css'
import 'tailwindcss/tailwind.css'
import './styles/index.css'
import './assets/iconFont/gv-iconfont.css'
import './assets/iconFont/iconfont.css'
const app = createApp(App);
// 挂载element-icon
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
setStore(store)
app.use(ElementPlus).use(store).mount('#app_main')
