<template>
  <div class="content">
    <geovis></geovis>
    <!-- <menus v-if="shomenu"></menus> -->
  </div>
</template>

<script lang="ts" setup>
import geovis from './components/geovis.vue'
import menus from './components/menu.vue'
// import hello from "./components/HelloWorld.vue";
import { defineProps, ref, defineEmits } from 'vue'
const shomenu = ref<boolean>(false)
let timer = setInterval(() => {
  if ((window as any).viewer) {
    shomenu.value = true
    clearInterval(timer)
  }
}, 200)
document.oncontextmenu = e => {
  e.preventDefault()
}
</script>
<style scoped>
</style>
<style lang="sass">
#app_main
  font-family: Avenir, Helvetica, Arial, sans-serif
  -webkit-font-smoothing: antialiased
  -moz-osx-font-smoothing: grayscale
  color: #2c3e50

  &,
  html,
  body,
  .content
    font-size: 14px !important
    width: 100% !important
    height: 100% !important
    padding: 0 !important
    margin: 0 !important
    color: white !important
    overflow: hidden !important

div,img,p,span
  user-select: none
</style>
