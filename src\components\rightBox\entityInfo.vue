<!--
 * @Author: 老范
 * @Date: 2025-05-12 14:27:16
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-05 09:30:57
 * @Description: 请填写简介
-->
<template>
  <div class="entityInfoBox">
    <div class="title">
      <span class="describe">实体信息</span>
      <span class="close" @click.stop="close">关闭</span>
    </div>
    <div class="body">
      <div class="table-title">
        <div>
          <span>名称</span>
          <span>{{ info.na }}</span>
        </div>
        <div>
          <span>阵营</span>
          <span>{{ info.si }}</span>
        </div>
        <div>
          <span>经度</span>
          <span>{{ info.lo }}</span>
        </div>
        <div>
          <span>纬度</span>
          <span>{{ info.la }}</span>
        </div>
        <div>
          <span>高度</span>
          <span>{{ info.al }}</span>
        </div>
        <div>
          <span>速度</span>
          <span>{{ info.speed }}</span>
        </div>
        <div>
          <span>俯仰</span>
          <span>{{ info.pi }}</span>
        </div>
        <div>
          <span>偏航</span>
          <span>{{ info.ya }}</span>
        </div>
        <div>
          <span>翻滚</span>
          <span>{{ info.ro }}</span>
        </div>
      </div>
      <div class="table-body"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  ref,
  onMounted,
  onUnmounted,
  defineProps,
  watch,
  defineEmits,
} from 'vue'
interface InfoType {
  la: number // 纬度
  lo: number // 经度
  al: number // 高度
  speed: number // 速度
  na: string
  // 模型状态: string
  si: string
  pi: number // 俯仰
  ya: number // 偏航
  ro: number // 翻滚
}
const emit = defineEmits(['closeBox'])
const ifUpdate: any = ref(true)
let props = defineProps<{
  id: number
}>()
let ws = ref<any>({})
let info = ref<InfoType | {}>({})
let frameId: number

const update = () => {
  if (!ifUpdate.value) return
  let clickEntity
  if (window.viewer) {
    clickEntity = window.viewer.entities.getById(props.id).properties
    info.value = {
      la: clickEntity.la.getValue(),
      lo: clickEntity.lo.getValue(),
      al: clickEntity.al.getValue(),
      speed: clickEntity.sp.getValue(),
      na: clickEntity.na.getValue(),
      // 模型状态:  clickEntity.na.getValue(),
      si: clickEntity.si.getValue(),
      pi: clickEntity.pi.getValue(),
      ya: clickEntity.ya.getValue(),
      ro: clickEntity.ro.getValue(),
    }
    frameId = requestAnimationFrame(update)
  }

  // console.log(
  //   '🚀 ~ update ~ clickEntity:',
  //   clickEntity.la.getValue(Cesium.JulianDate.now)
  // )
}
watch(
  () => props.id,
  newId => {
    if (newId) {
      console.log('🚀 ~ newId:', newId)
      cancelAnimationFrame(frameId)
      update()
    }
  },
  {
    immediate: true,
  }
)

onMounted(() => {
  // init()
  update()
})
onUnmounted(() => {
  // init()
  ifUpdate.value = false
  cancelAnimationFrame(frameId)
})
// const init = () => {
//   ws = new WebSocket(
//     `${(window as any).GVJ.URLS.socketServer}/getModelInstance`
//   )
//   ws.onopen = () => {
//     console.log('链接成功')
//     ws.send(JSON.stringify({ modelId: props.id.toString() }))
//   }
//   ws.onmessage = (e: any) => {
//     info.value = JSON.parse(e.data)
//   }
// }
// 关闭弹层
const close = () => {
  ifUpdate.value = false
  cancelAnimationFrame(frameId)
  // ws.close()
  emit('closeBox')
}
</script>
<style scoped>
.entityInfoBox {
  width: 11.875vw;
  height: 14.4259vh;
  min-width: 180px;
  min-height: 14vh;
  /* background-color: rgba(16, 37, 63, 0.7); */
  background-color: #003f63;
  /*margin: 20px 6px;*/
  position: absolute;
  right: 20px;
  top: 44.4815vh;
  z-index: 2002;
  box-sizing: border-box;
  border-radius: 5px 5px 0 0;
  color: #fff;
  padding-bottom: 20px;
}
.entityInfoBox .title {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 10px 10px;
  border-bottom: 2px solid #00ffff;
  margin-bottom: 10px;
}

.entityInfoBox .title > span:nth-child(1) {
  padding-left: 10px;
  border-left: 4px solid #0ff;
}
.entityInfoBox .title .close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}

.entityInfoBox .title > p {
  text-align: center;
}
.entityInfoBox .body .table-title {
  display: flex;
  justify-content: space-between;
  color: aqua;
  /* background: #0c2e4c; */
  background-color: #003f63;
  padding: 0 15px 5px;
  flex-direction: column;
  font-size: 12px;
  margin-bottom: 10px;
}

.entityInfoBox .body .table-title > div {
  height: 18px;
}

.entityInfoBox .body .table-title > div > span:nth-child(1) {
  border: 1px solid #00e0ff;
  border-radius: 3px;
  margin-right: 25px;
  padding: 0 8px;
}
.entityInfoBox .body {
  height: calc(100% - 188px);
}
.entityInfoBox .body .t-l {
  text-align: left !important;
  padding-left: 60px !important;
}
</style>
