<!--
 * @Author: 老范
 * @Date: 2025-05-12 15:42:07
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-21 10:56:09
 * @Description: 请填写简介
-->
<template>
  <div class="flex pl-2 bg">
    <div
      class="
        border-2
        w-10
        h-10
        flex
        justify-center
        items-center
        mr-2
        mt-2
        cursor-pointer
        item
      "
      v-for="item in arr"
      :key="item"
      @click="go(item.url)"
    >
      <el-tooltip class="item" effect="dark" :content="item.tittle">
        <div><img :src="geticon(item)" style="width: 20px" /></div>
      </el-tooltip>
    </div>
    <div class="popBox">
      <el-button class="btn" @click="scenarioBoxClick">想定信息</el-button>
      <el-button class="btn" @click="LogBoxClick">想定日志</el-button>
      <el-button class="btn" @click="entityBoxClick">实体列表</el-button>
      <el-button class="btn" @click="effectBoxClick">特效控制</el-button>
      <el-button class="btn" @click="openGlobalEffectConfig">全局特效配置</el-button>
      <el-button class="btn" @click="openTaskControl">任务控制</el-button>
      <el-button class="btn" @click="openEntityControl">实体控制</el-button>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
// const arr = [
//   { name: '图层',  url: 'http://***************:19101'},
//   { name: '插件管理', url: 'http://***************:19101' },
//   { name: '图标库', url: 'http://***************:4000' },
//   { name: '授权管理', url: 'http://***************:19102' },
// ]
const arr = window.GVJ.URLS.linkList
const go = (url: string): void => {
  if (!url) {
    return
  }
  window.open(url)
}
const scenarioBoxClick = () => {
  store.commit('SET_ScenarioShow', !store.state.app.ifScenarioShow)
}
const LogBoxClick = () => {
  store.commit('SET_LogBoxShow', !store.state.app.ifLogBoxShow)
}
const entityBoxClick = () => {
  store.commit('SET_EntityListBoxShow', !store.state.app.entityListShow)
}
const effectBoxClick = () => {
  store.commit('SET_EffectBoxShow', !store.state.app.effectBoxShow)
}
const openGlobalEffectConfig = () => {
  store.commit('SET_GlobalEffectBoxShow', !store.state.app.globalEffectBoxShow)
}
const openTaskControl = () => {
  store.commit('SET_TaskControlBoxShow', !store.state.app.taskControlBoxShow)
}
const openEntityControl = () => {
  store.commit('SET_EntityControlBoxShow', !store.state.app.entityControlBoxShow)
}
// 获取图标
const geticon = data => {
  return `/image/${data.icon}`
}
</script>

<style lang="sass" scoped>
.bg
  height: 92px
  width: 998px
  .item
    border-color: #144759c7
  .item:hover
    border-color: #74bedb !important

.popBox
  height: 35px
  margin-top: 8px

  .btn
    padding: 8px 10px !important
    background: #448bc5
    border-color:#448bc5
    color:#fff
    padding:0
    min-height:20px
</style>

<!-- <style lang="sass" scoped>
.bg
  height: 92px
  width: 998px
  background: url('../../assets/images/backside.png') repeat-x
  background-position-y: -10px
  .item
    border-color: #144759c7
  .item:hover
    border-color: #74bedb !important
</style> -->
