/**
 * eslint检测规则
 * 本规则基于plugin:vue/recommended和eslint:recommended配置
 * 查看本规则前请先熟悉plugin:vue/recommended和eslint:recommended
 * plugin:vue/recommended 参考 https://eslint.vuejs.org/rules/
 * eslint:recommended 参考 http://eslint.cn/docs/rules/
 */

module.exports = {
  // 不再往父级目录查找配置文件
  root: true,
  parserOptions: {
    parser: 'babel-eslint',
    sourceType: 'module',
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:vue/recommended',
    'plugin:prettier/recommended',
    'prettier/vue',
  ],

  globals: {
    CONF: 'writable',
    ol: 'readonly',
    HMap: 'readonly',
    RongIMLib: 'readonly',
  },

  /**
   * 具体规则
   * off或者0- 关闭规则
   * warn或者1 - 将规则打开为警告
   * error或者2 - 将规则打开为错误
   */
  rules: {
    // 'prettier/prettier': 'warn',
    'prettier/prettier': 'warn',
    /** vue相关 **/
    'vue/html-self-closing': 0,
    // 单行允许有的attr数量
    'vue/max-attributes-per-line': [
      1,
      {
        singleline: 10,
        multiline: {
          max: 3,
          allowFirstLine: true,
        },
      },
    ],
    // 关闭子组件必须换行
    'vue/singleline-html-element-content-newline': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/component-name-in-template-casing': [
      2,
      'kebab-case',
      {
        registeredComponentsOnly: false,
        ignores: [],
      },
    ],
    // 组件名称强制为Pascal case
    'vue/name-property-casing': [2, 'PascalCase'],
    // 对于v-html指令，开启警告
    'vue/no-v-html': 1,
    'vue/attributes-order': 0,
    'vue/mustache-interpolation-spacing': 0,
    'vue/no-unused-components': 1,
    /** 变量申明相关 **/
    // 变量申明要求使用 let 或 const 而不是 var
    'no-var': 2,
    // 不允许在块中function、var的申明
    'no-inner-declarations': [2, 'both'],
    // 变量不强制为camelcase
    camelcase: 0,
    // 构造函数首字母必须大写
    'new-cap': [
      2,
      {
        newIsCap: true,
        capIsNew: false,
      },
    ],
    // 尽量不要出现变量及函数参数定义了不使用的情况
    'no-unused-vars': [
      1,
      {
        vars: 'all',
        args: 'none',
      },
    ],
    // 禁止在变量定义之前使用它们
    'no-use-before-define': 0, // 不检测该类型
    // 要求所有的 var 声明出现在它们所在的作用域顶部，同时应尽量不要使用var申明变量，用let, const代替
    'vars-on-top': 2,
    'no-constant-condition': 1,

    /** 编码风格相关 **/
    // 不强制结尾分号，为保证代码风格一致性，建议统一不使用分号，但应注意不要混淆多行表达式
    semi: 0,
    // 代码缩进为2个空格
    indent: [1, 2, { SwitchCase: 1 }],
    // 箭头函数的箭头前后应该有空格
    'arrow-spacing': [
      2,
      {
        before: true,
        after: true,
      },
    ],
    // 花括号风格采用one true brace style
    'brace-style': [
      2,
      '1tbs',
      {
        allowSingleLine: true,
      },
    ],
    // 禁止空格和 tab 的混合缩进
    'no-mixed-spaces-and-tabs': 2,
    // 最大的连续空行限制为2行
    'no-multiple-empty-lines': [
      2,
      {
        max: 2,
      },
    ],
    // 不强制在对象和数组字面量中使用一致的拖尾逗号，但所有的风格应尽量保持统一
    'comma-dangle': 0,
    // 逗号后需要跟空格
    'comma-spacing': [
      2,
      {
        before: false,
        after: true,
      },
    ],
    // 允许在字符串和注释以外用一些不规则的空格，但是整体风格应尽量保持统一
    'no-irregular-whitespace': 0,
    // 逗号应该在语句后面
    'comma-style': [2, 'last'],
    // 强制所有控制语句花括号不能省略
    curly: [2, 'all'],
    // 要求 switch 语句中必须有 default 分支，或者有 // no default 注释
    'default-case': 2,
    // 禁止switch的case语句不break、return等结尾，若有意为之，请添加 // no break 类似注释语句
    'no-fallthrough': [
      2,
      {
        commentPattern: 'no[\\s\\w]*break',
      },
    ],
    // 不允许使用带标签的break，continue等语句
    'no-labels': 2,
    // 换行符应该在成员表达式中的点之前
    'dot-location': [2, 'property'],
    // 对象属性访问不强制使用点操作符，如 foo["bar"]
    'dot-notation': 0,
    // 除null外，对比时强制要求使用 === 和 !==
    eqeqeq: [1, 'always', { null: 'ignore' }],
    // 禁止在函数标识符和其调用之间有空格
    'func-call-spacing': [2, 'never'],
    // JSX 属性值强制使用双引号
    'jsx-quotes': [2, 'prefer-double'],
    // 对象字面量属性冒号前不能有空格，冒号后必须跟空格
    'key-spacing': [
      2,
      {
        beforeColon: false,
        afterColon: true,
      },
    ],
    // if else for 等关键字前后必须跟空格
    'keyword-spacing': [
      2,
      {
        before: true,
        after: true,
      },
    ],
    // 强制 getter 和 setter 在对象中成对出现
    'accessor-pairs': 2,
    // 不应该使用构造函数来构造新Array数组
    'no-array-constructor': 2,
    // 尽量不要使用arguments.caller和arguments.callee
    'no-caller': 1,
    // 不得对变量使用delete
    'no-delete-var': 2,
    // 禁止重复模块导入
    'no-duplicate-imports': 2,
    // 出现空函数告警，如果确实需要，函数体中可以加上注释
    'no-empty-function': 1,
    // 禁止使用空解构模式，如 const {a: {}} = foo;
    'no-empty-pattern': 2,
    // 禁止使用 eval()
    'no-eval': 2,
    // 禁止使用类似 eval() 的方法，如 setTimeout("alert('Hi!');", 100)
    'no-implied-eval': 2,
    // 禁止对 Function 对象使用 new 操作符，原因与eval相同
    'no-new-func': 2,
    // 禁用 with 语句
    'no-with': 2,
    // 允许强制转换变量为boolean类型，如 !!bar，但是很多情况没有必要
    'no-extra-boolean-cast': 0,
    // 禁止数字字面量中使用前导和末尾小数点，如 .5, 2., -.7等
    'no-floating-decimal': 2,
    // 禁止使用 __iterator__ 属性
    'no-iterator': 2,
    // 不允许使用 new require表达式
    'no-new-require': 2,
    // 禁止对 __dirname 和 __filename 进行字符串连接，应该用path.join等方法代替
    'no-path-concat': 2,
    // 关闭禁止直接调用 Object.prototypes 的内置属性规则
    'no-prototype-builtins': 0,
    // 不允许字符串文字中的八进制转义序列，如 "abc \234"
    'no-octal-escape': 2,
    // 不允许使用 __proto__ 属性，应该用Object.getPrototypeOf和Object.setPrototypeOf方法代替
    'no-proto': 2,
    // 不允许在return语句中使用赋值运算符，如 return foo = bar + 2;
    'no-return-assign': [2, 'always'],
    // 尽量不要在常规字符串中出现模板字面量占位符语法
    'no-template-curly-in-string': 1,
    // 尽量不要在 return、throw、continue 和 break 语句后出现不可达代码
    'no-unreachable': 1,
    // 尽量少地在对象中使用不必要的计算属性
    'no-useless-computed-key': 1,
    // 尽量不要使用没有意义的构造函数，如空的构造函数
    'no-useless-constructor': 1,
    // 尽量不要使用没必要的字符转义
    'no-useless-escape': 1,
    // 如果对象的属性位于同一行上，则不允许围绕点或在开头括号之前留出空白
    'no-whitespace-before-property': 2,
    // 使用 symbol 时必须有描述
    'symbol-description': 2,
    // 不限制使用console，但是在生成环境应该尽量删除
    'no-console': 0,
    // 不允许在生产环境的代码中有alert、confirm 和 prompt
    'no-alert': process.env.NODE_ENV === 'production' ? 2 : 0,
    // 不允许在生产环境的代码中有debugger
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,
  },
}
