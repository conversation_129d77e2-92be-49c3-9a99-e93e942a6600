var Timer = {
  data: {},
  start: function (key) {
    Timer.data[key] = new Date();
  },
  stop: function (key) {
    var time = Timer.data[key];
    if (time)
      Timer.data[key] = new Date() - time;
  },
  getTime: function (key) {
    return Timer.data[key];
  }
};
let deepClone = (obj) => {
  let objClone = Array.isArray(obj) ? [] : {};
  if (obj && typeof obj === "object") {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (obj[key] && typeof obj[key] === "object") {
          objClone[key] = this.deepClone(obj[key]);
        } else {
          objClone[key] = obj[key];
        }
      }
    }
  }
  return objClone;
}

let compareArray = function (arr1, arr2, operation = "not") {
  let aSet = new Map();
  arr1.map(item => aSet.set(item.id, item));

  let bSet = new Map();
  arr2.map(item => bSet.set(item.id, item));

  let _intersection = new Map();
  for (var item of aSet) {
    if (operation == "not") {
      if (!bSet.has(item[0])) {
        _intersection.set(item[0], item[1]);
      }
    } else if (operation == "and") {
      if (bSet.has(item[0])) {
        let target = bSet.get(item[0]);
        if (target.lon != item[1].lon || target.lat != item[1].lat || target.alt != item[1].alt) {
          _intersection.set(item[0], item[1]);
        }

      }
    }

  }
  return _intersection;
}
// 合并数据 以id为唯一标识
let concatArr = function (newData, data) {
  data = deepClone(data)
  let idlist = data.map(item => item.id)
  let deletelist = []
  newData.map(item => {
    let index = idlist.findIndex((ele) => ele == item.id)
    if (index) {
      data.splice(index, 1)
      idlist.splice(index, 1)
    }
  })
  return data.concat(newData)
}

var prevDataList = [];

onmessage = function (event) { //监听消息
  let json = event.data.data
  if (json) {
    // 是否修改了配置  如果是 则全部删除
    if (event.data.ischangeConfig) {
      json = prevDataList
      prevDataList = []
    }
    // 是否是多源数据  如果是 则需要数据合并
    if (!event.data.isAllData) {
      json = concatArr(json, prevDataList)
    }
    let discardData = compareArray(prevDataList, json);
    let newdata = compareArray(json, prevDataList);
    let updateddata = compareArray(json, prevDataList, "and");
    console.warn(`删除${discardData.size}个`);
    console.warn(`添加${newdata.size}个`);
    console.warn(`更新${updateddata.size}个`);
    let resultData = {
      removed: discardData,
      added: newdata,
      updated: updateddata
    }
    let obj = {
      type: 1,
      content: resultData,
      sumNum: json.length,
      list: json
    }
    self.postMessage(obj);
    prevDataList = json;
  } else {
    let resultData = {
      removed: [],
      added: [],
      updated: []
    }
    let obj = {
      type: 1,
      content: resultData,
      sumNum: json.length,
      list: json || []
    }
    self.postMessage(obj);
  }
}
