<!--
 * @Description: 全局特效配置组件
-->
<template>
  <div class="configBox">
    <div class="title">
      <span class="describe">全局特效配置</span>
      <span class="close" @click.stop="close">关闭</span>
    </div>
    <div class="body">
      <div class="table-title">
        <div class="group">
          <el-row class="group-label">
            全局实体配置
            <p></p>
          </el-row>
        </div>
        <div>
          <span>红方</span>
          <span>
            <el-switch
              v-model="redState"
              inline-prompt
              @change="val => changeSide(val, 'red')"
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>蓝方</span>
          <span>
            <el-switch
              v-model="blueState"
              inline-prompt
              @change="val => changeSide(val, 'blue')"
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>标牌</span>
          <span>
            <el-switch
              v-model="labelState"
              @change="changeLabel"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>

        <div class="group">
          <el-row class="group-label">
            全局特效配置
            <p></p>
          </el-row>
        </div>
        <div>
          <span>通讯</span>
          <span>
            <el-switch
              v-model="communicationState"
              inline-prompt
              @change="changeCommunication"
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>航路</span>
          <span>
            <el-switch
              v-model="routeState"
              inline-prompt
              @change="changeRoute"
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>探测</span>
          <span>
            <el-switch
              v-model="probState"
              @change="changeProb"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>交火</span>
          <span>
            <el-switch
              v-model="fireState"
              @change="changeFire"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>尾迹</span>
          <span>
            <el-switch
              v-model="trailState"
              @change="changeTrail"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div>
          <span>雷达</span>
          <span>
            <el-switch
              v-model="sensorState"
              @change="changeSensor"
              inline-prompt
              active-text="开"
              inactive-text="关"
          /></span>
        </div>
        <div class="group">
          <el-row class="group-label">
            尾迹点数限制
            <p></p>
          </el-row>
        </div>
        <el-slider
          v-model="trailPointCount"
          :min="0"
          :max="5000"
          :format-tooltip="value => `${value}点`"
          @change="changeTrailPointCount"
        />
        <div class="group">
          <el-row class="group-label">
            模型基础尺寸控制
            <p></p>
          </el-row>
        </div>
        <el-slider
          v-model="modelSize"
          :min="1"
          :max="5"
          :step="1"
          :format-tooltip="value => `${value}倍`"
          @change="changeModelSize"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineEmits, watch } from 'vue'

const emit = defineEmits(['closeBox'])
const ifUpdate = ref(true)

const redState = ref(true) // 红方实体
const blueState = ref(true) // 蓝方实体
const labelState = ref(true) // 标牌

const communicationState = ref(true) // 通讯
const routeState = ref(true) // 航路
const probState = ref(true) // 探测
const fireState = ref(true) // 交火
const trailState = ref(true) // 尾迹
const sensorState = ref(true) // 雷达

// 初始化尾迹点数为300（与trailDataProcessor.js中的默认值保持一致）
const trailPointCount = ref(300)
// 模型尺寸
const modelSize = ref(1)

// 改变模型尺寸
const changeModelSize = val => {
  // 如果modelManager存在，更新模型尺寸
  if (window.modelManager) {
    const config = window.modelManager.updateModelSize(val)
    console.log(
      `模型尺寸已调整为${val}级别: minimumPixelSize=${config.minimumPixelSize}, maximumScale=${config.maximumScale}`
    )
  }
}

// 改变模型尾迹
const changeTrailPointCount = val => {
  // 如果modelManager存在，更新尾迹点数
  if (window.modelManager && window.modelManager.trailPrimitive) {
    // 向Worker发送消息，更新maxPoints
    window.modelManager.trailPrimitive.worker.postMessage({
      type: 'UPDATE_MAX_POINTS',
      maxPoints: val,
    })
  }
}


// 改变阵营
const changeSide = (val: boolean, type: string) => {
  // 获取所有实体
  const entities = window.viewer.entities.values

  // 遍历所有实体，找出红方实体并设置显示/隐藏
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]
    // 检查是否为红方实体
    if (
      entity.properties &&
      entity.properties.si &&
      entity.properties.si.getValue() === type
    ) {
      // 设置实体显示/隐藏
      entity.show = val

      // 设置标牌显示/隐藏
      if (entity.label) {
        entity.label.show = val
      }

      // 获取实体ID
      const entityId = entity.id

      // 处理尾迹
      if (window.modelManager && window.modelManager.trailPrimitive) {
        const trail = window.modelManager.trailPrimitive.trails.get(entityId)
        if (trail && trail.polyLine) {
          trail.polyLine.show = val
        }
      }

      // 处理相关特效（通讯、探测、交火、雷达等）
      if (window.effectManager && window.effectManager.effects) {
        const effects = window.effectManager.effects
        console.log(Array.from(effects.values()))

        const prefix = entityId + '_'

        effects.forEach((effect: any, key: string) => {
          if (key.startsWith(prefix)) {
            // 根据特效类型处理
            if (effect.entity) {
              effect.entity.show = val
            }
            if (effect.graphic) {
              effect.graphic.visible = val
            }
          }
        })
      }
    }
  }
}

// 改变通讯
const changeCommunication = (val: boolean) => {
  // 获取所有通讯特效并设置显示状态
  const effects = window.effectManager.effects
  effects.forEach((effect: any, key: string) => {
    if (key.endsWith('_0')) {
      // 通讯特效类型为0
      effect.entity.show = val
    }
  })
}

// 改变航路
const changeRoute = (val: boolean) => {
  const entities = window.viewer.entities.values.filter(i =>
    i.id.includes('_initRoute')
  )
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]
    entity.show = val
  }
}

// 改变探测
const changeProb = (val: boolean) => {
  const effects = window.effectManager.effects
  effects.forEach((effect: any, key: string) => {
    if (key.endsWith('_1')) {
      // 探测特效类型为1
      effect.entity.show = val
    }
  })
}

// 改变交火
const changeFire = (val: boolean) => {
  const effects = window.effectManager.effects
  effects.forEach((effect: any, key: string) => {
    if (key.endsWith('_2')) {
      // 交火特效类型为2
      effect.entity.show = val
    }
  })
}

// 改变尾迹
const changeTrail = (val: boolean) => {
  // 尾迹特效处理
  if (!window?.modelManager) return console.warn('查找模型尾迹失败')
  const trails = Array.from(window.modelManager.trailPrimitive.trails.values())
  for (let i = 0; i < trails.length; i++) {
    const trail = trails[i]
    if (trail?.polyLine) trail.polyLine.show = val
  }
}

// 改变标牌
const changeLabel = (val: boolean) => {
  // 获取所有实体并设置标牌显示状态
  const entities = window.viewer.entities.values
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]
    if (entity.label) {
      entity.label.show = val
    }
  }
}

// 改变雷达
const changeSensor = (val: boolean) => {
  const effects = window.effectManager.effects
  effects.forEach((effect: any, key: string) => {
    if (key.endsWith('_6')) {
      // 雷达特效类型为6
      if (effect.graphic) {
        effect.graphic.visible = val
      }
    }
  })
}

onMounted(() => {
  // 初始化全局特效状态
})

onUnmounted(() => {
  ifUpdate.value = false
})

// 关闭弹层
const close = () => {
  ifUpdate.value = false
  emit('closeBox')
}
</script>
<style scoped>
.configBox {
  width: 11.875vw;
  height: 22.4259vh;
  background-color: #003f63;
  position: absolute;
  right: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2002;
  box-sizing: border-box;
  border-radius: 5px 5px 0 0;
  color: #fff;
  padding-bottom: 20px;
}
.configBox .title {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 10px 10px;
  border-bottom: 2px solid #00ffff;
  margin-bottom: 10px;
}

.configBox .title > span:nth-child(1) {
  padding-left: 10px;
  border-left: 4px solid #0ff;
}
.configBox .title .close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}

.configBox .title > p {
  text-align: center;
}
.entityInfoBox .body {
  height: 188px;
}
.configBox .body .table-title > div {
  height: 24px;
}
.configBox .body .table-title {
  display: flex;
  justify-content: space-between;
  color: aqua;
  background-color: #003f63;
  padding: 0 15px;
  flex-direction: column;
  font-size: 12px;
  margin-bottom: 10px;
}
.configBox .body .table-title .group .group-label {
  align-items: center;
}
.configBox .body .table-title .group .group-label p {
  margin-left: 5px;
  flex: 1;
  background: #0b84af;
  height: 2px;
}
.configBox .body .table-title > div > span:nth-child(1) {
  border: 1px solid #00e0ff;
  border-radius: 3px;
  margin-right: 25px;
  padding: 0 8px;
}
</style>
