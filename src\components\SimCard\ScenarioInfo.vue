<template>
  <div class="scenarioBox">
    <el-row class="title">
      <img src="/image/scenarioInfo_icon.png" alt="" class="icon" width="16" />
      <span>想定信息</span>
    </el-row>
    <div class="contentBox">
      <el-row class="item">
        <div class="label">想定名称：</div>
        <div class="value" :title="scenarioInfo.scenarioName || ''">
          {{ scenarioInfo.scenarioName || '-' }}
        </div>
      </el-row>
      <el-row class="item description-item">
        <div class="label">想定描述：</div>
        <div
          class="value description-value"
          :title="scenarioInfo.scenarioDescribe || ''"
        >
          {{ scenarioInfo.scenarioDescribe || '-' }}
        </div>
      </el-row>
      <el-row class="item">
        <div class="label">运行模式：</div>
        <div class="value">
          {{ scenarioInfo.scenarioName ? scenarioInfo.modeText || '-' : '-' }}
        </div>
      </el-row>
      <el-row class="timeline-row">
        <img src="/image/chart_line_img.png" alt="" class="timeline-img left" />

        <el-row class="timeline-text"
          ><img src="/image/time_icon.png" alt="" /><span
            >仿真持续时间</span
          ></el-row
        >
        <img src="/image/chart_line_img.png" alt="" class="timeline-img" />
      </el-row>
      <el-row class="duration">{{
        formatDuration(scenarioInfo.realTime)
      }}</el-row>
      <el-row class="item">
        <div class="label">仿真时间：</div>
        <div class="value date">
          {{
            props.scenarioInfo?.startTime &&
            scenarioInfo?.realTime !== undefined
              ? dayjs(
                  props.scenarioInfo.startTime + scenarioInfo.realTime * 1000
                )
                  .subtract(8, 'hour')
                  .format('YYYY-MM-DD HH:mm:ss')
              : currentTime
          }}
        </div>
      </el-row>
      <el-row class="item">
        <div class="label">天文时间：</div>
        <div class="value date">
          {{ currentTime }}
        </div>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import duration from 'dayjs/plugin/duration'
import { ref, onMounted, onUnmounted } from 'vue'

// 配置dayjs插件
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(duration)

// 将秒数转换为 YYYY-MM-DD HH:mm:ss 格式
const formatDuration = (seconds: number): string => {
  // 使用dayjs duration处理秒数
  const dur = dayjs.duration(seconds, 'seconds')
  const years = Math.floor(dur.asYears())
  const months = dur.months()
  const days = dur.days()
  const hours = dur.hours()
  const minutes = dur.minutes()
  const secs = dur.seconds()

  return `${years.toString().padStart(4, '0')}-${months
    .toString()
    .padStart(2, '0')}-${days.toString().padStart(2, '0')} ${hours
    .toString()
    .padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`
}

const props = defineProps({
  scenarioInfo: {
    type: Object,
    default: '',
  },
})

// 添加当前时间的响应式变量
const currentTime = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'))

// 添加定时器变量
let timer: NodeJS.Timeout | null = null

// 组件挂载时启动定时器
onMounted(() => {
  // 每秒更新一次时间
  timer = setInterval(() => {
    currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
  }, 1000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer !== null) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style lang="less" scoped>
.scenarioBox {
  width: 100%;
  height: 300px;
  background-color: #002941;
  border: 1px solid var(--app-border-color);
  border-left: none;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  .title {
    height: 32px;
    align-items: center;
    background-image: linear-gradient(90deg, #1f77ad 0%, #002942 100%);
    padding-left: 9px;
    .icon {
      margin-right: 8px;
    }
  }
  .contentBox {
    font-size: 15px;
    padding: 12px 10px 0;

    .item {
      display: flex;
      align-items: center;
      margin-bottom: 9px;
      width: 100%;

      .label {
        flex-shrink: 0; // 防止标签缩小
        white-space: nowrap;
      }

      .value {
        flex: 1; // 使用flex: 1替代flex-grow: 1
        min-width: 0; // 关键属性，允许flex项目缩小到比内容更小
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
        font-weight: 600;
      }
      .value.date {
        background-image: linear-gradient(0deg, #0a5a88 0%, #00456d 100%);
        border: solid 1px #36b6ff;
        padding-left: 8px;
      }
      .value.description-value {
        white-space: normal; // 允许换行
        display: -webkit-box;
        -webkit-line-clamp: 2; // 限制为两行
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .item.description-item {
      align-items: flex-start;
    }

    .duration {
      font-size: 22px;
      justify-content: center;
      margin-bottom: 9px;
      font-weight: 600;
    }
    // 添加新的样式
    .timeline-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 5px;
    }

    .timeline-img {
      width: 23%; // 调整图片宽度
      height: 3px;
      flex-shrink: 0;
    }
    .timeline-img.left {
      transform: rotate(180deg);
    }
    .timeline-text {
      img {
        margin: 0 5px;
      }
      flex-grow: 1;
      margin: 0;
      white-space: nowrap;
      align-items: center;
    }
  }
}
</style>
