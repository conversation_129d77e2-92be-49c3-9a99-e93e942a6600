<template>
  <div
    class="
      p-2
      flex
      items-center
      justify-center
      text-blue-300
      opacity-80
      flex-nowrap
    "
  >
    <div class="contents" v-if="arr.length">
      <div
        v-for="(item, index) in arr"
        :key="item.name"
        @click="toolsClick(item, index)"
      >
        <el-tooltip
          class="item"
          effect="dark"
          placement="top-start"
          :content="item.name"
        >
          <img
            class="nofocus"
            :class="{ borders: getborder(item) }"
            :src="geticon(item)"
          />
        </el-tooltip>
      </div>
      <el-tooltip
        class="item"
        effect="dark"
        placement="top-start"
        content="关闭所有已开启插件"
      >
        <i class="el-icon el-icon-close" @click="close()" />
      </el-tooltip>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const arr = computed(() => {
  return store.state.app.toolsList
})
const urlls = '/assets/plugin/ts/icon.png'
const geticon = data => {
  let obj = window.GVJ.allPlugin.tools.find(ele => ele.plugName == data.name)
  if (obj) {
    return `/assets/plugin/${obj.annexName}/icon.png`
  } else {
    let icon = require('../../assets/plugin/hyperlink.png')
    return icon
  }
}
// 插件点击切换状态 点击后只保存一个 其他已开启过的是hide状态
const toolsClick = (plugin: any, index: number) => {
  let tool = window.dePluginManager.Tools.find(
    (ele: any) => ele.name == plugin.name
  )
  let list = store.state.app.openList
  if (tool) {
    // 插件切换之前 添加状态
    // 注意：插件只显示一个  所以需要将所有工具类的插件筛选出来清除再添加
    let arr = window.dePluginManager.Tools.map(ele => ele.name)
    list = list.filter(ele => arr.indexOf(ele) === -1)
    // 判断是显示的时候再添加
    if (tool.state !== 0) {
      list.push(tool.name)
    }
    store.commit('SET_OPENLIST', list)
    // 修改插件状态
    window.dePluginManager.changeTool(tool)
  }
}
// 关闭所有插件
const close = () => {
  let list = store.state.app.openList
  let arr = window.dePluginManager.Tools.map(ele => ele.name)
  list = list.filter(ele => arr.indexOf(ele) === -1)
  store.commit('SET_OPENLIST', list)
  // 关闭所有插件
  window.dePluginManager.Tools.map(ele => {
    window.dePluginManager.closeTool(ele)
  })
}
// 获取是否选中
const getborder = data => {
  return store.state.app.openList.indexOf(data.name) !== -1
}
</script>
<style scoped lang='sass'>
.contents
  width: auto
  position: absolute
  height: 40px
  background: rgba(0, 0, 0, 0.5)
  display: flex
  justify-content: center
  margin: -15px 0 0 0
  padding: 0 40px
  > div
    cursor: pointer
    width: 36px
    height: 36px
    margin: 5px
    padding: 3px
    overflow: hidden
    img
      width: 100%
      height: 100%

  .el-icon
    position: absolute
    top: 5px
    right: 5px

  .nofocus
    outline: none !important
    border: 1px solid rgba(0, 0, 0, 0)

  .borders
    transform: translateY(-60px)
    filter: drop-shadow(white 0 60px)
</style>
